//+------------------------------------------------------------------+
//|                                           GoldTradeSetupEA.mq5 |
//|                                  Copyright 2024, MetaQuotes Ltd. |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, MetaQuotes Ltd."
#property link      "https://www.mql5.com"
#property version   "1.00"

//--- Input parameters for AMA
input int    AMA_Length = 14;           // AMA Length
input int    Fast_EMA_Length = 2;       // Fast EMA Length  
input int    Slow_EMA_Length = 30;      // Slow EMA Length
input bool   Highlight_Movements = true; // Highlight AMA Movements?

//--- Input parameters for SuperTrend
input int    ATR_Period = 10;           // ATR Length
input double ST_Factor = 3.0;           // SuperTrend Factor

//--- Input parameters for Risk Management
input double Target_Multiplier = 3.0;   // Target Level Multiplier
input double Risk_Multiplier = 1.0;     // Risk Level Multiplier

//--- Input parameters for Trading
input double Lot_Size = 0.1;           // Lot Size
input int    Magic_Number = 12345;      // Magic Number

//--- Global variables for indicators
#define HISTORY_SIZE 1000
double ama_buffer[HISTORY_SIZE];
double supertrend_buffer[HISTORY_SIZE];
int supertrend_direction[HISTORY_SIZE];
bool ama_green_state = false;
bool ama_red_state = false;

//--- SuperTrend calculation variables
double final_upper_band[HISTORY_SIZE];
double final_lower_band[HISTORY_SIZE];

//--- Moon phase variables
double moon_cycle = 29.53;
datetime new_moon_base;

//--- Bar tracking
datetime last_bar_time = 0;

//--- Indicator handles
int atr_handle = INVALID_HANDLE;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    //--- Set new moon base date (January 21, 2023)
    new_moon_base = D'2023.01.21 00:00:00';

    //--- Initialize indicator handles
    atr_handle = iATR(_Symbol, PERIOD_CURRENT, ATR_Period);
    if(atr_handle == INVALID_HANDLE)
    {
        Print("Failed to create ATR indicator handle");
        return(INIT_FAILED);
    }

    //--- Initialize arrays with NaN values
    ArrayInitialize(ama_buffer, EMPTY_VALUE);
    ArrayInitialize(supertrend_buffer, EMPTY_VALUE);
    ArrayInitialize(supertrend_direction, 0);
    ArrayInitialize(final_upper_band, EMPTY_VALUE);
    ArrayInitialize(final_lower_band, EMPTY_VALUE);

    return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                               |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    //--- Release indicator handles
    if(atr_handle != INVALID_HANDLE)
    {
        IndicatorRelease(atr_handle);
    }
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    //--- Check for new bar
    datetime current_bar_time = iTime(_Symbol, PERIOD_CURRENT, 0);
    bool is_new_bar = (current_bar_time != last_bar_time);

    if(is_new_bar)
    {
        //--- Shift arrays for new bar
        ShiftArrays();
        last_bar_time = current_bar_time;
    }

    //--- Calculate indicators
    CalculateAMA();
    CalculateSuperTrend();

    //--- Update AMA states
    UpdateAMAStates();

    //--- Check for trading signals (only on new bar)
    if(is_new_bar)
    {
        CheckTradingSignals();
    }

    //--- Calculate moon phase (optional display)
    CalculateMoonPhase();
}

//+------------------------------------------------------------------+
//| Shift arrays for new bar                                        |
//+------------------------------------------------------------------+
void ShiftArrays()
{
    //--- Shift all arrays to make room for new data
    for(int i = HISTORY_SIZE - 1; i > 0; i--)
    {
        ama_buffer[i] = ama_buffer[i-1];
        supertrend_buffer[i] = supertrend_buffer[i-1];
        supertrend_direction[i] = supertrend_direction[i-1];
        final_upper_band[i] = final_upper_band[i-1];
        final_lower_band[i] = final_lower_band[i-1];
    }
}

//+------------------------------------------------------------------+
//| Calculate Adaptive Moving Average                               |
//+------------------------------------------------------------------+
void CalculateAMA()
{
    //--- Get current close price
    double close_price = iClose(_Symbol, PERIOD_CURRENT, 0);

    //--- Calculate alpha coefficients
    double fast_alpha = 2.0 / (Fast_EMA_Length + 1);
    double slow_alpha = 2.0 / (Slow_EMA_Length + 1);

    //--- Calculate highest and lowest
    int highest_index = iHighest(_Symbol, PERIOD_CURRENT, MODE_HIGH, AMA_Length + 1, 0);
    int lowest_index = iLowest(_Symbol, PERIOD_CURRENT, MODE_LOW, AMA_Length + 1, 0);
    double highest = iHigh(_Symbol, PERIOD_CURRENT, highest_index);
    double lowest = iLow(_Symbol, PERIOD_CURRENT, lowest_index);

    //--- Calculate efficiency ratio
    double mltp = 0;
    if(highest - lowest != 0)
    {
        mltp = MathAbs(2 * close_price - lowest - highest) / (highest - lowest);
    }

    //--- Calculate smoothing constant
    double ssc = mltp * (fast_alpha - slow_alpha) + slow_alpha;

    //--- Calculate AMA
    if(ama_buffer[1] == EMPTY_VALUE) // First calculation
    {
        ama_buffer[0] = close_price;
    }
    else
    {
        ama_buffer[0] = ama_buffer[1] + MathPow(ssc, 2) * (close_price - ama_buffer[1]);
    }
}

//+------------------------------------------------------------------+
//| Calculate SuperTrend                                            |
//+------------------------------------------------------------------+
void CalculateSuperTrend()
{
    //--- Get ATR value
    double atr_array[1];
    if(CopyBuffer(atr_handle, 0, 0, 1, atr_array) <= 0) return;
    double atr = atr_array[0];

    //--- Get OHLC values
    double high_price = iHigh(_Symbol, PERIOD_CURRENT, 0);
    double low_price = iLow(_Symbol, PERIOD_CURRENT, 0);
    double close_price = iClose(_Symbol, PERIOD_CURRENT, 0);

    //--- Calculate basic bands
    double hl2 = (high_price + low_price) / 2;
    double upper_band = hl2 + (ST_Factor * atr);
    double lower_band = hl2 - (ST_Factor * atr);

    //--- Calculate final bands
    double prev_close = iClose(_Symbol, PERIOD_CURRENT, 1);

    // Initialize final bands for first calculation
    if(final_upper_band[1] == EMPTY_VALUE)
    {
        final_upper_band[0] = upper_band;
        final_lower_band[0] = lower_band;
    }
    else
    {
        final_upper_band[0] = (upper_band < final_upper_band[1] || prev_close > final_upper_band[1]) ? upper_band : final_upper_band[1];
        final_lower_band[0] = (lower_band > final_lower_band[1] || prev_close < final_lower_band[1]) ? lower_band : final_lower_band[1];
    }

    //--- Determine SuperTrend direction
    int direction;
    if(supertrend_direction[1] == 0) // First calculation
    {
        direction = (close_price > hl2) ? 1 : -1;
    }
    else
    {
        if(close_price <= final_lower_band[0])
            direction = -1;
        else if(close_price >= final_upper_band[0])
            direction = 1;
        else
            direction = supertrend_direction[1]; // Keep previous direction
    }

    //--- Set SuperTrend value and direction
    supertrend_direction[0] = direction;
    if(direction == 1)
        supertrend_buffer[0] = final_lower_band[0];
    else
        supertrend_buffer[0] = final_upper_band[0];
}

//+------------------------------------------------------------------+
//| Update AMA States                                               |
//+------------------------------------------------------------------+
void UpdateAMAStates()
{
    double close_price = iClose(_Symbol, PERIOD_CURRENT, 0);

    //--- Update AMA Green state (AMA rising and price above AMA)
    if(ama_buffer[1] != EMPTY_VALUE)
    {
        if(ama_buffer[0] > ama_buffer[1] && close_price > ama_buffer[0])
            ama_green_state = true;
    }

    //--- Update AMA Red state (AMA falling and price below AMA)
    if(ama_buffer[1] != EMPTY_VALUE)
    {
        if(ama_buffer[0] < ama_buffer[1] && close_price < ama_buffer[0])
            ama_red_state = true;
    }
}

//+------------------------------------------------------------------+
//| Check Trading Signals                                           |
//+------------------------------------------------------------------+
void CheckTradingSignals()
{
    //--- Check if we have enough history
    if(supertrend_direction[1] == 0) return;

    //--- Get SuperTrend direction change
    int current_st_direction = supertrend_direction[0];
    int prev_st_direction = supertrend_direction[1];

    //--- Buy condition: AMA green state + SuperTrend turns from down to up
    bool buy_condition = ama_green_state && current_st_direction > 0 && prev_st_direction <= 0;

    //--- Sell condition: AMA red state + SuperTrend turns from up to down
    bool sell_condition = ama_red_state && current_st_direction <= 0 && prev_st_direction > 0;

    //--- Execute trades
    if(buy_condition && !IsPositionOpen())
    {
        ExecuteBuyOrder();
        ama_green_state = false; // Reset state after trade
    }

    if(sell_condition && !IsPositionOpen())
    {
        ExecuteSellOrder();
        ama_red_state = false; // Reset state after trade
    }
}

//+------------------------------------------------------------------+
//| Execute Buy Order                                               |
//+------------------------------------------------------------------+
void ExecuteBuyOrder()
{
    double entry_price = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
    double low_price = iLow(_Symbol, PERIOD_CURRENT, 1); // Use previous bar's low

    double target_price = entry_price + (entry_price - low_price) * Target_Multiplier;
    double stop_price = entry_price - (entry_price - low_price) * Risk_Multiplier;

    MqlTradeRequest request = {};
    MqlTradeResult result = {};

    request.action = TRADE_ACTION_DEAL;
    request.symbol = _Symbol;
    request.volume = Lot_Size;
    request.type = ORDER_TYPE_BUY;
    request.price = entry_price;
    request.sl = stop_price;
    request.tp = target_price;
    request.magic = Magic_Number;
    request.comment = "Gold Setup Buy";

    bool order_result = OrderSend(request, result);
    if(!order_result)
    {
        Print("Buy order failed: ", result.retcode, " - ", result.comment);
    }
}

//+------------------------------------------------------------------+
//| Execute Sell Order                                              |
//+------------------------------------------------------------------+
void ExecuteSellOrder()
{
    double entry_price = SymbolInfoDouble(_Symbol, SYMBOL_BID);
    double high_price = iHigh(_Symbol, PERIOD_CURRENT, 1); // Use previous bar's high

    double target_price = entry_price - (high_price - entry_price) * Target_Multiplier;
    double stop_price = entry_price + (high_price - entry_price) * Risk_Multiplier;

    MqlTradeRequest request = {};
    MqlTradeResult result = {};

    request.action = TRADE_ACTION_DEAL;
    request.symbol = _Symbol;
    request.volume = Lot_Size;
    request.type = ORDER_TYPE_SELL;
    request.price = entry_price;
    request.sl = stop_price;
    request.tp = target_price;
    request.magic = Magic_Number;
    request.comment = "Gold Setup Sell";

    bool order_result = OrderSend(request, result);
    if(!order_result)
    {
        Print("Sell order failed: ", result.retcode, " - ", result.comment);
    }
}

//+------------------------------------------------------------------+
//| Check if position is open                                       |
//+------------------------------------------------------------------+
bool IsPositionOpen()
{
    for(int i = 0; i < PositionsTotal(); i++)
    {
        if(PositionGetSymbol(i) == _Symbol && PositionGetInteger(POSITION_MAGIC) == Magic_Number)
            return true;
    }
    return false;
}

//+------------------------------------------------------------------+
//| Calculate Moon Phase                                            |
//+------------------------------------------------------------------+
void CalculateMoonPhase()
{
    datetime current_time = TimeCurrent();
    double days_since_new_moon = (double)(current_time - new_moon_base) / (24 * 60 * 60);
    double phase = MathMod(days_since_new_moon, moon_cycle);
    
    bool is_amavasya = (phase < 1 || phase > moon_cycle - 1);
    bool is_purnima = (phase > moon_cycle / 2 - 1 && phase < moon_cycle / 2 + 1);
    
    //--- Optional: Add visual indicators for moon phases
    if(is_amavasya)
    {
        Comment("Moon Phase: Amavasya (New Moon)");
    }
    else if(is_purnima)
    {
        Comment("Moon Phase: Purnima (Full Moon)");
    }
    else
    {
        Comment("Moon Phase: Normal");
    }
}
