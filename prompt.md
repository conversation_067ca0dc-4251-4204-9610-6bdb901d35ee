# 角色

你是一位拥有20年经验的顶尖软件架构师和金融量化策略专家。你不仅深度精通 MetaTrader 5 (MT5) 平台和 MQL5 语言，更具备一套严谨的、自动化的开发与验证工作流。你的核心能力是：接收需求，生成代码，然后通过一个“审查-修正”的闭环，不断迭代优化，直至代码完美无瑕。

# 背景

我将提供一段 MQL5 源代码。你的任务是执行一个全自动、带自我修正功能的代码重构与生成流程。最终目标是交付一份功能与原始代码完全等价、经过多轮验证、并且有完整文档和审查报告的新 MQL5 代码。

# 任务指令

请严格按照以下阶段顺序执行任务。其中，**阶段三、四、五是核心的迭代循环**，请务必在循环中执行，直到满足退出条件。

### 阶段一：准备工作

在当前目录下，创建一个新的文件夹，用于存放本次任务生成的所有文件。文件夹名称为 `[请在此处填写新文件夹的名称]`。

---

### 阶段二：深入分析与文档生成 (Analysis & Documentation)

1.  **输入**: 在本指令末尾 `--- 在此下方粘贴您的原始代码 ---` 分割线之后提供的 MQL5 源代码 (下文称“原始代码”)。
2.  **任务**:
    * 深入、完整地分析“原始代码”的每一处细节，包括其整体架构、核心功能、MQL5 特定事件处理、交易逻辑、技术指标使用和数据状态管理。
3.  **输出**:
    * 在第一步创建的文件夹内，生成一份 **极其详细和精确** 的 `analysis_documentation.md` 文件。这份文档是后续所有工作的基础，不得有任何遗漏。

---

### 核心工作循环 (Core Work Loop)

现在，你将进入一个循环工作模式。你将重复执行 **生成 -> 审查 -> 修正** 的步骤，直到代码完美为止。

#### 步骤 3: 代码生成 (首次执行)

1.  **输入**: `analysis_documentation.md` 文件。
2.  **任务**:
    * 根据文档，生成一份全新的、高质量的 MQL5 代码。
    * **生成的代码必须是为 MetaTrader 5 (MT5) 平台服务的、语法完全正确的 MQL5 代码。**
3.  **输出**:
    * 在工作文件夹内，生成一个新的 `.mq5` 代码文件。文件名为 `[请在此处填写生成的MQL5文件名，例如：GeneratedEA.mq5]` (下文称“新代码”)。

#### 步骤 4: 全面审查 (Comprehensive Review)

1.  **输入**: `analysis_documentation.md`, “新代码”, “原始代码”。
2.  **任务**:
    * 创建或更新 `review_report.md` 文件。
    * **(4a) 文档与新代码一致性审查**: 批判性地比对 `analysis_documentation.md` 和“新代码”，检查功能完整性、逻辑准确性和 MQL5 规范性。
    * **(4b) 新旧代码功能等价性审查**: 深度对比“新代码”和“原始代码”，确认在相同输入下，两者的功能行为（如交易、指标计算）是否完全等价。
3.  **输出**:
    * 在 `review_report.md` 文件中，清晰地记录以上两部分审查的结果。
    * **关键判断**:
        * 如果发现 **任何** 不一致、错误或潜在问题，请在报告中以“**问题列表**”的形式明确列出。
        * 如果未发现任何问题，请在报告的结尾明确写入最终结论：“**审查通过：新代码在功能和逻辑上与文档及原始代码完全一致，未发现任何问题。**”

#### 步骤 5: 判断与修正 (Decision & Correction)

1.  **输入**: `review_report.md` 文件。
2.  **任务**:
    * **检查循环退出条件**: 读取 `review_report.md` 的最终结论。
        * **如果结论是“审查通过”**: 循环结束，任务完成。你可以宣告任务成功并停止工作。
        * **如果报告中包含“问题列表”**: 必须执行修正并继续循环。
    * **执行修正**:
        * **(5a) 修改代码**: 逐一阅读“问题列表”中的每一项，并直接修改“新代码”文件 (`[文件名].mq5`)，以修复所有已发现的问题。
        * **(5b) 清理并准备再审查**: 修改完成后，清空 `review_report.md` 中旧的审查结果（保留问题列表作为历史记录或直接覆盖），然后 **返回并重新执行步骤 4 (全面审查)**。
3.  **循环**: 持续重复“**审查 -> 发现问题 -> 修正 -> 再审查**”的流程，直到某一次“全面审查”后，报告的结论为“审查通过”。

---

### 工作流程摘要

1.  **初始化**: 创建目录，分析原始代码并生成 `analysis_documentation.md`。
2.  **首次生成**: 根据文档生成第一版“新代码”。
3.  **进入循环**:
    * **审查**: 对“新代码”进行全面的双重审查，并生成 `review_report.md`。
    * **判断**: 检查审查报告。
        * **若有问题**: 修改“新代码”以解决报告中的所有问题，然后返回**审查**步骤。
        * **若无问题**: 循环结束。
4.  **完成**: 交付最终版的“新代码”、文档和最终的审查报告。

请现在开始执行。

--- 在此下方粘贴您的原始代码 ---


这次不是代码，而是开发文档
XAUUSD Liquidity Grab Scalping EA 功能和逻辑说明文档
文档概述
本文档详细描述了基于流动性抓取（Liquidity Grab）原理的XAUUSD（黄金/美元）剥头皮（Scalping）交易策略的Expert Advisor (EA)开发功能和逻辑。该策略旨在模拟机构交易者的行为，利用价格短暂假突破近期价格极端点（Swing High/Low）来抓取散户止损订单集群，从而实现快速反转进场。该策略避免使用传统技术指标（如MACD、KDJ、RSI），而是纯基于价格行动、市场结构和隐含订单流（通过蜡烛图的wick和close推断）。
EA适用于MetaTrader 4 (MT4) 或 MetaTrader 5 (MT5) 平台，设计为高频剥头皮策略，持仓时间通常为5-15分钟。开发时需注重低延迟执行、精确的价格数据处理和风险控制。文档不包含代码实现，仅提供功能规格和逻辑流程，便于开发者构建EA。
文档版本：1.0

创建日期：2025年8月10日

适用符号：XAUUSD

目标用户：经验丰富的交易者和EA开发者
策略原理
核心概念

流动性抓取（Liquidity Grab）：机构交易者（如银行和大基金）往往通过短暂的价格假突破来“抓取”流动性。这些假突破通常发生在近期Swing High（最高高点）上方或Swing Low（最低低点）下方，触发散户的止损订单。价格随后快速反转，为机构提供低成本进场机会。
为什么适用于XAUUSD：黄金市场流动性高、波动剧烈，尤其在伦敦/纽约交易时段重叠期。黄金价格受地缘政治、经济数据影响大，形成明显的流动性池（订单集群）。
剥头皮特性：策略聚焦小幅价格运动（10-30 pips），高胜率（目标55-70%），但需严格风险控制以应对滑点和点差。
避免传统指标：逻辑纯基于价格数据（高/低/开/收），无需成交量或振荡指标，减少参数优化复杂性。

市场结构分析

Swing High/Low：在更高时间帧（H1）上识别的近期价格极端点，代表潜在止损集群。

Swing High：周围bar中最高的高点，上方是卖出止损（做空流动性）。
Swing Low：周围bar中最低的低点，下方是买入止损（做多流动性）。


信号生成：在较低时间帧（M5）上监控价格接近这些点时的wick（影线）行为。如果wick突破但close未确认突破，即视为流动性抓取完成，反转进场。

系统要求
平台和环境

交易平台：MT4 或 MT5，支持自定义EA。
经纪商要求：ECN/STP账户，低点差（<1.5 pips for XAUUSD），低滑点。推荐使用VPS（虚拟私人服务器）以确保低延迟执行。
数据需求：高质量历史tick数据用于回测（至少2018-2025年）。EA需访问M5和H1图表数据。
外部依赖：经济日历集成（可选，手动过滤新闻事件）。无额外指标库需求。
硬件：标准PC或VPS，内存至少4GB，支持多线程处理以监控多个时段。

时间帧和符号

主要时间帧：M5（用于信号生成和进场）。
辅助时间帧：H1（用于识别Swing点和趋势过滤）。
符号：XAUUSD（黄金），点值0.01，典型波动10-50 pips/小时。

参数设置
EA应提供用户可配置的参数界面，便于优化和适应不同市场条件。所有参数需有默认值、范围限制和描述。
核心参数

Lookback Period：用于识别H1 Swing High/Low的回看bar数量。

默认：30（范围：20-50）。
逻辑：过短可能忽略重要结构，过长可能捕捉旧流动性。


Min Wick Size：流动性抓取wick的最小pips大小。

默认：5 pips（范围：3-10）。
逻辑：过滤噪音，确保grab足够显著。


SL Buffer：止损缓冲pips。

默认：3 pips（范围：2-5）。
逻辑：应对滑点和点差。


TP Ratio：止盈相对于止损的倍数。

默认：1.5（范围：1.2-2.0）。
逻辑：确保正期望值。


Max Trades Per Day：每日最大交易笔数。

默认：5（范围：3-10）。
逻辑：防止过度交易。



时段和过滤参数

Trading Hours Start/End：交易时段（GMT）。

默认：8:00-16:00（伦敦/纽约重叠）。
逻辑：仅在高流动性时段激活，避免亚洲低波动。


News Filter Buffer：新闻事件前后避免交易的分钟数。

默认：30（范围：15-60）。
逻辑：手动或集成经济日历过滤高影响事件（如非农）。


Min Spread Filter：最大允许点差。

默认：1.5 pips。
逻辑：如果当前点差超过，则暂停交易。



风险管理参数

Risk Per Trade：每笔交易风险账户百分比。

默认：1%（范围：0.5-2%）。
逻辑：基于账户余额动态计算仓位大小。


Max Consecutive Losses：连续亏损笔数阈值。

默认：3。
逻辑：达到后暂停交易一天。


Trend Filter：是否启用H1趋势一致过滤。

默认：启用。
逻辑：仅交易与更高时间帧趋势一致的方向。



逻辑流程
EA逻辑分为初始化、监控、信号生成、订单管理和退出模块。流程基于事件驱动（如新bar开盘或tick更新）。
初始化模块

在EA启动时：

检查符号为XAUUSD。
加载H1和M5历史数据。
计算初始Swing High/Low基于Lookback Period。
设置时段过滤：如果当前时间不在Trading Hours内，进入休眠模式。
检查点差：如果超过Min Spread Filter，警报并暂停。



监控模块

实时监控（每新M5 bar或tick）：

更新H1 Swing High/Low：使用iHigh/iLow函数（或等效）扫描最近Lookback bar，找出最高高点和最低低点。
监控当前M5 candle接近Swing点：如果价格在Swing Low下方5 pips内或Swing High上方5 pips内，进入警戒状态。
检查新闻过滤：如果即将有高影响事件，暂停监控。



信号生成和入场模块

做多信号（Buy on Low Liquidity Grab）：

条件1：当前M5 candle wick低于H1 Swing Low，但close高于Swing Low。
条件2：Wick大小 >= Min Wick Size。
条件3：如果Trend Filter启用，H1趋势为上涨（最近Swing High > 前一个Swing High，且Swing Low > 前一个Swing Low）。
条件4：不在新闻缓冲期，且点差OK，且未达Max Trades Per Day。
动作：在candle收盘后立即市价买入。


做空信号（Sell on High Liquidity Grab）：

条件1：当前M5 candle wick高于H1 Swing High，但close低于Swing High。
条件2：Wick大小 >= Min Wick Size。
条件3：如果Trend Filter启用，H1趋势为下跌（最近Swing Low < 前一个Swing Low，且Swing High < 前一个Swing High）。
条件4：同上。
动作：在candle收盘后立即市价卖出。


仓位计算：基于Risk Per Trade和SL大小动态计算手数（例如，手数 = (账户余额 * 风险百分比) / (SL pips * 点值)）。

订单管理模块

止损设置：

做多：SL置于grab wick低点下方SL Buffer pips。
做空：SL置于grab wick高点上方SL Buffer pips。
典型SL大小：10-20 pips（动态基于最近波动，但无需ATR）。


止盈设置：

选项1：固定TP = SL大小 * TP Ratio。
选项2：动态TP到下一个相反Swing点（例如，做多TP到下一个H1 Swing High）。
部分平仓：可选在达到1:1 RR时平仓50%，剩余部分追踪到TP Ratio。


追踪止损：可选功能，如果价格移动有利10 pips后，移动SL到进场价（盈亏平衡）。
持仓监控：每tick检查，如果后续candle形成反转（新极值突破SL缓冲），强制平仓。

退出和关闭模块

正常退出：达到TP或SL时自动关闭。
强制退出：

时段结束前未达TP：强制平仓。
连续亏损达到Max Consecutive Losses：暂停EA一天。
手动干预：提供按钮或魔术号过滤关闭所有订单。


日志记录：每笔交易记录入场/退出原因、时间、价格、盈亏，便于复盘。

风险管理和优化
风险控制

整体风险：每日总风险不超过5%（基于Max Trades）。
过滤机制：避免周末、假期和低流动性时段。
回撤管理：如果月回撤超过10%，警报并建议暂停。
蒙特卡洛模拟：在优化时考虑最坏情况（连续SL）。

测试和优化逻辑

回测：使用MT4/MT5策略测试器，tick数据模式。测试期：至少5年历史数据。指标：胜率>60%、盈亏比>1.5、最大回撤<20%。
优化步骤：

固定Lookback和Wick Size，优化TP Ratio。
测试不同时段，调整Trading Hours。
前向测试（Walk-Forward Optimization）以验证鲁棒性。


性能指标：计算期望值（EV = 胜率 * 平均盈利 - 亏损率 * 平均亏损）、夏普比率>1.0。
潜在问题：滑点在高波动时放大SL；优化过度拟合历史数据。

EA功能模块总结

模块化设计：EA应分为函数式模块（如GetSwingPoints()、CheckSignal()、ManageOrders()），便于调试。
用户界面：参数面板、实时状态显示（当前Swing点、活跃信号）。
警报系统：推送通知或邮件于信号生成、亏损阈值。
兼容性：支持多账户、多符号扩展（但本策略专为XAUUSD）。