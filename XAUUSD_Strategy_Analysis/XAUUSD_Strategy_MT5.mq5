//+------------------------------------------------------------------+
//|                                           XAUUSD_Strategy_MT5.mq5 |
//|                                  Copyright 2024, MetaQuotes Ltd. |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, MetaQuotes Ltd."
#property link      "https://www.mql5.com"
#property version   "1.00"

#include <Trade\Trade.mqh>

//--- Input parameters
input ENUM_TIMEFRAMES Timeframe = PERIOD_M10;     // Trading timeframe
input int      MACD_Fast = 12;             // MACD Fast EMA period
input int      MACD_Slow = 26;             // MACD Slow EMA period
input int      MACD_Signal = 9;            // MACD Signal SMA period
input int      RSI_Period = 14;            // RSI period
input double   RSI_Overbought = 65.0;      // RSI overbought level
input double   RSI_Oversold = 35.0;        // RSI oversold level
input int      BB_Period = 20;             // Bollinger Bands period
input double   BB_Deviation = 2.0;         // Bollinger Bands deviation
input int      ATR_Period = 14;            // ATR period
input double   StopLoss_ATR_Mult = 3.0;    // Stop Loss ATR multiplier
input double   TakeProfit_ATR_Mult = 5.0;  // Take Profit ATR multiplier
input double   LotSize = 0.1;              // Lot size
input int      MagicNumber = 123456;       // Magic number

//--- Global variables
CTrade trade;
int macd_handle, rsi_handle, bb_handle, atr_handle;
double macd_main[], macd_signal[], rsi_values[], bb_upper[], bb_lower[], bb_middle[], atr_values[];
int trade_number = 0;
datetime last_bar_time = 0;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    //--- Set trade parameters
    trade.SetExpertMagicNumber(MagicNumber);
    trade.SetDeviationInPoints(10);
    trade.SetTypeFilling(ORDER_FILLING_FOK);
    
    //--- Create indicator handles
    macd_handle = iMACD(_Symbol, Timeframe, MACD_Fast, MACD_Slow, MACD_Signal, PRICE_CLOSE);
    rsi_handle = iRSI(_Symbol, Timeframe, RSI_Period, PRICE_CLOSE);
    bb_handle = iBands(_Symbol, Timeframe, BB_Period, 0, BB_Deviation, PRICE_CLOSE);
    atr_handle = iATR(_Symbol, Timeframe, ATR_Period);
    
    //--- Check if handles are valid
    if(macd_handle == INVALID_HANDLE || rsi_handle == INVALID_HANDLE || 
       bb_handle == INVALID_HANDLE || atr_handle == INVALID_HANDLE)
    {
        Print("Error creating indicator handles");
        return INIT_FAILED;
    }
    
    //--- Set array as series
    ArraySetAsSeries(macd_main, true);
    ArraySetAsSeries(macd_signal, true);
    ArraySetAsSeries(rsi_values, true);
    ArraySetAsSeries(bb_upper, true);
    ArraySetAsSeries(bb_lower, true);
    ArraySetAsSeries(bb_middle, true);
    ArraySetAsSeries(atr_values, true);
    
    Print("XAUUSD Strategy MT5 initialized successfully on timeframe: ", EnumToString(Timeframe));
    return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    //--- Release indicator handles
    IndicatorRelease(macd_handle);
    IndicatorRelease(rsi_handle);
    IndicatorRelease(bb_handle);
    IndicatorRelease(atr_handle);
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    //--- Check for new bar
    datetime current_bar_time = iTime(_Symbol, Timeframe, 0);
    if(current_bar_time == last_bar_time)
        return;
    last_bar_time = current_bar_time;
    
    //--- Get indicator values
    if(!GetIndicatorValues())
        return;
    
    //--- Check trading conditions
    CheckTradingConditions();
}

//+------------------------------------------------------------------+
//| Get indicator values                                             |
//+------------------------------------------------------------------+
bool GetIndicatorValues()
{
    //--- Get MACD values
    if(CopyBuffer(macd_handle, 0, 0, 3, macd_main) < 3 ||
       CopyBuffer(macd_handle, 1, 0, 3, macd_signal) < 3)
    {
        Print("Error getting MACD values");
        return false;
    }
    
    //--- Get RSI values
    if(CopyBuffer(rsi_handle, 0, 0, 2, rsi_values) < 2)
    {
        Print("Error getting RSI values");
        return false;
    }
    
    //--- Get Bollinger Bands values
    if(CopyBuffer(bb_handle, 0, 0, 2, bb_middle) < 2 ||
       CopyBuffer(bb_handle, 1, 0, 2, bb_upper) < 2 ||
       CopyBuffer(bb_handle, 2, 0, 2, bb_lower) < 2)
    {
        Print("Error getting Bollinger Bands values");
        return false;
    }
    
    //--- Get ATR values
    if(CopyBuffer(atr_handle, 0, 0, 2, atr_values) < 2)
    {
        Print("Error getting ATR values");
        return false;
    }
    
    return true;
}

//+------------------------------------------------------------------+
//| Check trading conditions                                         |
//+------------------------------------------------------------------+
void CheckTradingConditions()
{
    //--- Get current prices and close price
    MqlTick tick;
    if(!SymbolInfoTick(_Symbol, tick))
        return;

    double current_close = iClose(_Symbol, Timeframe, 0);
    double atr_current = atr_values[0];

    //--- Calculate stop loss and take profit distances
    double stop_loss_distance = StopLoss_ATR_Mult * atr_current;
    double take_profit_distance = TakeProfit_ATR_Mult * atr_current;

    //--- Check MACD signals (crossover logic)
    bool macd_buy = (macd_main[1] <= macd_signal[1] && macd_main[0] > macd_signal[0]);
    bool macd_sell = (macd_main[1] >= macd_signal[1] && macd_main[0] < macd_signal[0]);

    //--- Check RSI signals
    bool rsi_oversold = (rsi_values[0] < RSI_Oversold);
    bool rsi_overbought = (rsi_values[0] > RSI_Overbought);

    //--- Check Bollinger Bands signals (use close price as in original)
    bool bb_lower_touch = (current_close < bb_lower[0]);
    bool bb_upper_touch = (current_close > bb_upper[0]);

    //--- Combine buy conditions (OR logic as in original)
    bool buy_condition = (macd_buy || rsi_oversold || bb_lower_touch);

    //--- Combine sell conditions (OR logic as in original)
    bool sell_condition = (macd_sell || rsi_overbought || bb_upper_touch);

    //--- Check current position for this EA only
    bool has_position = HasCurrentPosition();
    ENUM_POSITION_TYPE position_type = GetCurrentPositionType();

    //--- Execute trading logic
    if(!has_position)
    {
        if(buy_condition)
        {
            OpenBuyPosition(tick.ask, stop_loss_distance, take_profit_distance);
        }
        else if(sell_condition)
        {
            OpenSellPosition(tick.bid, stop_loss_distance, take_profit_distance);
        }
    }
    else
    {
        //--- Check exit conditions
        if(position_type == POSITION_TYPE_BUY && sell_condition)
        {
            CloseBuyPosition(tick.bid);
        }
        else if(position_type == POSITION_TYPE_SELL && buy_condition)
        {
            CloseSellPosition(tick.ask);
        }
    }
}

//+------------------------------------------------------------------+
//| Open buy position                                                |
//+------------------------------------------------------------------+
void OpenBuyPosition(double ask_price, double sl_distance, double tp_distance)
{
    double entry_price = ask_price;
    double stop_loss = entry_price - sl_distance;
    double take_profit = entry_price + tp_distance;

    if(trade.Buy(LotSize, _Symbol, entry_price, stop_loss, take_profit, "Buy Entry"))
    {
        trade_number++;
        string alert_message = StringFormat(
            "Trade No: %d\nSignal: Entry Long - Enter buy trade\nDate/Time: %s\nPrice: %.5f",
            trade_number, TimeToString(TimeCurrent(), TIME_DATE|TIME_MINUTES), entry_price
        );
        Print(alert_message);
        SendNotification(alert_message);
    }
}

//+------------------------------------------------------------------+
//| Open sell position                                               |
//+------------------------------------------------------------------+
void OpenSellPosition(double bid_price, double sl_distance, double tp_distance)
{
    double entry_price = bid_price;
    double stop_loss = entry_price + sl_distance;
    double take_profit = entry_price - tp_distance;

    if(trade.Sell(LotSize, _Symbol, entry_price, stop_loss, take_profit, "Sell Entry"))
    {
        trade_number++;
        string alert_message = StringFormat(
            "Trade No: %d\nSignal: Entry Short - Enter sell trade\nDate/Time: %s\nPrice: %.5f",
            trade_number, TimeToString(TimeCurrent(), TIME_DATE|TIME_MINUTES), entry_price
        );
        Print(alert_message);
        SendNotification(alert_message);
    }
}

//+------------------------------------------------------------------+
//| Close buy position                                               |
//+------------------------------------------------------------------+
void CloseBuyPosition(double bid_price)
{
    double exit_price = bid_price;

    if(trade.PositionClose(_Symbol))
    {
        string alert_message = StringFormat(
            "Trade No: %d\nSignal: Exit Long - Exit buy trade\nDate/Time: %s\nPrice: %.5f",
            trade_number, TimeToString(TimeCurrent(), TIME_DATE|TIME_MINUTES), exit_price
        );
        Print(alert_message);
        SendNotification(alert_message);
    }
}

//+------------------------------------------------------------------+
//| Close sell position                                              |
//+------------------------------------------------------------------+
void CloseSellPosition(double ask_price)
{
    double exit_price = ask_price;

    if(trade.PositionClose(_Symbol))
    {
        string alert_message = StringFormat(
            "Trade No: %d\nSignal: Exit Short - Exit sell trade\nDate/Time: %s\nPrice: %.5f",
            trade_number, TimeToString(TimeCurrent(), TIME_DATE|TIME_MINUTES), exit_price
        );
        Print(alert_message);
        SendNotification(alert_message);
    }
}

//+------------------------------------------------------------------+
//| Check if current EA has position                                 |
//+------------------------------------------------------------------+
bool HasCurrentPosition()
{
    for(int i = 0; i < PositionsTotal(); i++)
    {
        if(PositionSelectByTicket(PositionGetTicket(i)))
        {
            if(PositionGetString(POSITION_SYMBOL) == _Symbol &&
               PositionGetInteger(POSITION_MAGIC) == MagicNumber)
            {
                return true;
            }
        }
    }
    return false;
}

//+------------------------------------------------------------------+
//| Get current position type                                        |
//+------------------------------------------------------------------+
ENUM_POSITION_TYPE GetCurrentPositionType()
{
    for(int i = 0; i < PositionsTotal(); i++)
    {
        if(PositionSelectByTicket(PositionGetTicket(i)))
        {
            if(PositionGetString(POSITION_SYMBOL) == _Symbol &&
               PositionGetInteger(POSITION_MAGIC) == MagicNumber)
            {
                return (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE);
            }
        }
    }
    return -1; // No position found
}
