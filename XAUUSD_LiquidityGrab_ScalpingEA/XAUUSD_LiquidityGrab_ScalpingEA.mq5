//+------------------------------------------------------------------+
//|                                XAUUSD_LiquidityGrab_ScalpingEA.mq5 |
//|                                  Copyright 2025, Expert Advisor |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2025, Expert Advisor"
#property link      "https://www.mql5.com"
#property version   "1.00"
#property description "XAUUSD Liquidity Grab Scalping EA - 基于流动性抓取的剥头皮策略"

//--- 包含必要的库
#include <Trade\Trade.mqh>

//--- 创建交易对象
CTrade trade;

//--- 输入参数
input group "=== 核心交易参数 ==="
input int    LookbackPeriod = 30;           // Swing点识别回看周期 (20-50)
input double MinWickSize = 5.0;             // 最小wick大小 (pips, 3.0-10.0)
input double SLBuffer = 3.0;                // 止损缓冲 (pips, 2.0-5.0)
input double TPRatio = 1.5;                 // 止盈倍数 (1.2-2.0)
input int    MaxTradesPerDay = 5;           // 每日最大交易数 (3-10)

input group "=== 时段和过滤参数 ==="
input int    TradingStartHour = 8;          // 交易开始时间 (GMT)
input int    TradingEndHour = 16;           // 交易结束时间 (GMT)
input int    NewsFilterBuffer = 30;         // 新闻过滤缓冲 (分钟, 15-60)
input double MinSpreadFilter = 1.5;         // 最大允许点差 (pips)

input group "=== 风险管理参数 ==="
input double RiskPerTrade = 1.0;            // 每笔交易风险 (%, 0.5-2.0)
input int    MaxConsecutiveLosses = 3;      // 最大连续亏损数
input bool   TrendFilterEnabled = true;     // 启用趋势过滤
input bool   M15TrendFilterEnabled = true;  // 启用M15趋势过滤

input group "=== 高级过滤参数 ==="
input double MinVolatility = 8.0;           // 最小波动率 (ATR pips, 5.0-15.0)
input double MinReversalSpeed = 2.0;        // 最小反转速度 (pips/秒, 1.0-5.0)
input int    CooldownBars = 3;              // 信号冷却周期 (M5 bars, 2-10)

input group "=== 高级订单管理 ==="
input bool   EnablePartialClose = true;     // 启用部分平仓
input double PartialCloseRatio = 0.5;       // 部分平仓比例 (0.3-0.7)
input bool   EnableTrailingStop = true;     // 启用追踪止损
input double TrailingTrigger = 10.0;        // 追踪止损触发 (pips, 8.0-15.0)
input int    MaxHoldingMinutes = 15;        // 最大持仓时间 (分钟, 10-30)
input bool   EnableATRMode = false;         // 启用ATR动态SL/TP模式
input double ATRMultiplier = 2.0;           // ATR止损倍数 (1.5-3.0)

input group "=== 其他设置 ==="
input int    MagicNumber = 20250810;        // 魔术号
input string CommentPrefix = "LiqGrab_";     // 订单注释前缀

//--- 全局变量
double SwingHigh = 0.0;                     // 当前Swing High
double SwingLow = 0.0;                      // 当前Swing Low
int LastSwingHighBar = -1;                  // 最后Swing High的bar索引
int LastSwingLowBar = -1;                   // 最后Swing Low的bar索引
int TradesToday = 0;                        // 今日交易计数
int ConsecutiveLosses = 0;                  // 连续亏损计数
datetime LastTradeDate = 0;                 // 最后交易日期
bool EAActive = true;                       // EA激活状态
double PointValue = 0.0;                    // 点值
double PipSize = 0.0;                       // pip大小
datetime NextNewsTime = 0;                  // 下一个新闻事件时间
bool NewsFilterActive = false;              // 新闻过滤激活状态

//--- 高级过滤变量
datetime LastBuySignalTime = 0;             // 最后买入信号时间
datetime LastSellSignalTime = 0;            // 最后卖出信号时间
double LastSwingHighUsed = 0.0;             // 最后使用的Swing High
double LastSwingLowUsed = 0.0;              // 最后使用的Swing Low

//--- 持仓跟踪结构
struct PositionTracker
{
    ulong ticket;                           // 持仓票号
    datetime openTime;                      // 开仓时间
    bool partialCloseExecuted;              // 部分平仓执行状态
    bool trailingStopActivated;             // 追踪止损激活状态
};

//--- 订单管理变量
PositionTracker positionTrackers[];         // 持仓跟踪数组

//--- 技术指标句柄
int ATRHandle = INVALID_HANDLE;             // ATR指标句柄

//--- 枚举类型
enum ENUM_SIGNAL_TYPE
{
    SIGNAL_NONE = 0,    // 无信号
    SIGNAL_BUY = 1,     // 买入信号
    SIGNAL_SELL = 2     // 卖出信号
};

enum ENUM_TREND_DIRECTION
{
    TREND_UPTREND = 1,      // 上涨趋势
    TREND_DOWNTREND = -1,   // 下跌趋势
    TREND_SIDEWAYS = 0      // 横盘
};

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    // 验证交易品种
    if(Symbol() != "XAUUSD")
    {
        Print("错误: 此EA仅适用于XAUUSD交易品种");
        return INIT_FAILED;
    }
    
    // 初始化点值和pip大小
    PointValue = SymbolInfoDouble(Symbol(), SYMBOL_TRADE_TICK_VALUE);
    PipSize = SymbolInfoDouble(Symbol(), SYMBOL_POINT) * 10; // XAUUSD的1pip = 10点
    
    // 设置交易参数
    trade.SetExpertMagicNumber(MagicNumber);
    trade.SetDeviationInPoints(30);
    trade.SetTypeFilling(ORDER_FILLING_FOK);
    
    // 参数验证
    if(!ValidateParameters())
    {
        Print("错误: 参数验证失败");
        return INIT_FAILED;
    }
    
    // 初始化技术指标
    ATRHandle = iATR(Symbol(), PERIOD_M5, 14);
    if(ATRHandle == INVALID_HANDLE)
    {
        Print("错误: ATR指标初始化失败");
        return INIT_FAILED;
    }

    // 初始化Swing点
    if(!InitializeSwingPoints())
    {
        Print("错误: Swing点初始化失败");
        return INIT_FAILED;
    }

    // 重置日计数器
    ResetDailyCounters();

    // 设置定时器（每分钟执行一次）
    EventSetTimer(60);

    Print("XAUUSD流动性抓取剥头皮EA初始化成功");
    Print("当前Swing High: ", SwingHigh, ", Swing Low: ", SwingLow);

    return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    // 清理定时器
    EventKillTimer();

    // 释放技术指标句柄
    if(ATRHandle != INVALID_HANDLE)
    {
        IndicatorRelease(ATRHandle);
        ATRHandle = INVALID_HANDLE;
    }

    Print("EA卸载，原因代码: ", reason);
}

//+------------------------------------------------------------------+
//| Expert timer function                                            |
//+------------------------------------------------------------------+
void OnTimer()
{
    // 定时任务：每分钟执行一次

    // 检查并重置日计数器
    CheckNewDay();

    // 更新Swing点（减少OnTick中的计算负担）
    static datetime lastSwingUpdate = 0;
    datetime currentTime = TimeCurrent();

    // 每5分钟更新一次Swing点
    if(currentTime - lastSwingUpdate >= 300)
    {
        UpdateSwingPointsTimer();
        lastSwingUpdate = currentTime;
    }

    // 检查EA健康状态
    CheckEAHealth();

    // 清理过期的日志文件（每小时执行一次）
    static datetime lastLogCleanup = 0;
    if(currentTime - lastLogCleanup >= 3600)
    {
        CleanupOldLogs();
        lastLogCleanup = currentTime;
    }
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    // 检查EA是否激活
    if(!EAActive)
        return;

    // 检查数据完整性
    if(!CheckDataIntegrity())
        return;

    // 检查是否为新的一天
    CheckNewDay();
    
    // 检查交易时段
    if(!IsInTradingHours())
        return;
    
    // 检查点差
    if(!IsSpreadAcceptable())
        return;
    
    // 检查是否达到每日最大交易数
    if(TradesToday >= MaxTradesPerDay)
        return;
    
    // 检查连续亏损
    if(ConsecutiveLosses >= MaxConsecutiveLosses)
        return;

    // 检查新闻过滤
    if(!IsNewsFilterClear())
        return;

    // 更新Swing点（仅在新H1 bar时）
    UpdateSwingPoints();
    
    // 检查流动性抓取信号
    ENUM_SIGNAL_TYPE signal = CheckLiquidityGrabSignal();
    
    // 执行交易
    if(signal != SIGNAL_NONE)
    {
        ExecuteOrder(signal);
    }
    
    // 管理现有订单
    ManageOrders();
}

//+------------------------------------------------------------------+
//| 参数验证函数                                                      |
//+------------------------------------------------------------------+
bool ValidateParameters()
{
    if(LookbackPeriod < 20 || LookbackPeriod > 50)
    {
        Print("错误: LookbackPeriod必须在20-50之间");
        return false;
    }

    if(MinWickSize < 3.0 || MinWickSize > 10.0)
    {
        Print("错误: MinWickSize必须在3.0-10.0之间");
        return false;
    }

    if(SLBuffer < 2.0 || SLBuffer > 5.0)
    {
        Print("错误: SLBuffer必须在2.0-5.0之间");
        return false;
    }

    if(TPRatio < 1.2 || TPRatio > 2.0)
    {
        Print("错误: TPRatio必须在1.2-2.0之间");
        return false;
    }

    if(RiskPerTrade < 0.5 || RiskPerTrade > 2.0)
    {
        Print("错误: RiskPerTrade必须在0.5-2.0之间");
        return false;
    }

    // 验证新增的高级参数
    if(MinVolatility < 0.0 || MinVolatility > 20.0)
    {
        Print("错误: MinVolatility必须在0.0-20.0之间");
        return false;
    }

    if(MinReversalSpeed < 0.0 || MinReversalSpeed > 10.0)
    {
        Print("错误: MinReversalSpeed必须在0.0-10.0之间");
        return false;
    }

    if(CooldownBars < 0 || CooldownBars > 20)
    {
        Print("错误: CooldownBars必须在0-20之间");
        return false;
    }

    if(PartialCloseRatio < 0.1 || PartialCloseRatio > 0.9)
    {
        Print("错误: PartialCloseRatio必须在0.1-0.9之间");
        return false;
    }

    if(TrailingTrigger < 5.0 || TrailingTrigger > 30.0)
    {
        Print("错误: TrailingTrigger必须在5.0-30.0之间");
        return false;
    }

    if(MaxHoldingMinutes < 0 || MaxHoldingMinutes > 60)
    {
        Print("错误: MaxHoldingMinutes必须在0-60之间");
        return false;
    }

    if(ATRMultiplier < 1.0 || ATRMultiplier > 5.0)
    {
        Print("错误: ATRMultiplier必须在1.0-5.0之间");
        return false;
    }

    return true;
}

//+------------------------------------------------------------------+
//| 初始化Swing点                                                    |
//+------------------------------------------------------------------+
bool InitializeSwingPoints()
{
    // 获取H1时间帧的最高点和最低点
    int highestBar = iHighest(Symbol(), PERIOD_H1, MODE_HIGH, LookbackPeriod, 1);
    int lowestBar = iLowest(Symbol(), PERIOD_H1, MODE_LOW, LookbackPeriod, 1);
    
    if(highestBar == -1 || lowestBar == -1)
    {
        Print("错误: 无法获取历史数据");
        return false;
    }
    
    SwingHigh = iHigh(Symbol(), PERIOD_H1, highestBar);
    SwingLow = iLow(Symbol(), PERIOD_H1, lowestBar);
    LastSwingHighBar = highestBar;
    LastSwingLowBar = lowestBar;
    
    return true;
}

//+------------------------------------------------------------------+
//| 重置日计数器                                                      |
//+------------------------------------------------------------------+
void ResetDailyCounters()
{
    datetime currentTime = TimeCurrent();
    MqlDateTime timeStruct;
    TimeToStruct(currentTime, timeStruct);
    
    datetime todayStart = StructToTime(timeStruct);
    todayStart = todayStart - (timeStruct.hour * 3600 + timeStruct.min * 60 + timeStruct.sec);
    
    if(LastTradeDate < todayStart)
    {
        TradesToday = 0;
        LastTradeDate = todayStart;
        Print("日计数器已重置");
    }
}

//+------------------------------------------------------------------+
//| 检查新的一天                                                      |
//+------------------------------------------------------------------+
void CheckNewDay()
{
    static datetime lastDay = 0;
    datetime currentDay = iTime(Symbol(), PERIOD_D1, 0);
    
    if(lastDay != currentDay)
    {
        ResetDailyCounters();
        ConsecutiveLosses = 0; // 新的一天重置连续亏损
        EAActive = true;       // 重新激活EA
        lastDay = currentDay;
    }
}

//+------------------------------------------------------------------+
//| 检查是否在交易时段                                                |
//+------------------------------------------------------------------+
bool IsInTradingHours()
{
    MqlDateTime timeStruct;
    TimeToStruct(TimeCurrent(), timeStruct);
    
    int currentHour = timeStruct.hour;
    
    if(TradingStartHour <= TradingEndHour)
    {
        return (currentHour >= TradingStartHour && currentHour < TradingEndHour);
    }
    else
    {
        return (currentHour >= TradingStartHour || currentHour < TradingEndHour);
    }
}

//+------------------------------------------------------------------+
//| 检查点差是否可接受                                                |
//+------------------------------------------------------------------+
bool IsSpreadAcceptable()
{
    double spread = SymbolInfoInteger(Symbol(), SYMBOL_SPREAD) * SymbolInfoDouble(Symbol(), SYMBOL_POINT);
    double spreadPips = spread / PipSize;

    return (spreadPips <= MinSpreadFilter);
}

//+------------------------------------------------------------------+
//| 检查新闻过滤是否清除                                              |
//+------------------------------------------------------------------+
bool IsNewsFilterClear()
{
    if(NewsFilterBuffer <= 0)
        return true; // 新闻过滤已禁用

    datetime currentTime = TimeCurrent();

    // 检查是否在新闻事件缓冲期内
    if(NewsFilterActive && currentTime < NextNewsTime)
    {
        return false;
    }

    // 检查即将到来的新闻事件（简化实现，实际应集成经济日历）
    // 这里实现一个基本的时间段过滤，避免在重要新闻发布时间交易
    MqlDateTime timeStruct;
    TimeToStruct(currentTime, timeStruct);

    // 避免在以下时间交易（GMT）：
    // 美国非农就业数据：通常在第一个周五 13:30
    // 美联储利率决议：通常在 19:00
    // 欧洲央行利率决议：通常在 12:45

    int hour = timeStruct.hour;
    int minute = timeStruct.min;
    int dayOfWeek = timeStruct.day_of_week;

    // 避免在重要新闻时间段交易
    if((hour == 13 && minute >= 15 && minute <= 45) ||  // 美国数据发布时间
       (hour == 19 && minute >= 45) || (hour == 20 && minute <= 15) ||  // 美联储时间
       (hour == 12 && minute >= 30) || (hour == 13 && minute <= 0))     // 欧洲央行时间
    {
        if(!NewsFilterActive)
        {
            NewsFilterActive = true;
            NextNewsTime = currentTime + (NewsFilterBuffer * 60);
            Print("新闻过滤激活，暂停交易至: ", TimeToString(NextNewsTime));
        }
        return false;
    }

    // 重置新闻过滤状态
    if(NewsFilterActive && currentTime >= NextNewsTime)
    {
        NewsFilterActive = false;
        Print("新闻过滤解除，恢复交易");
    }

    return true;
}

//+------------------------------------------------------------------+
//| 更新Swing点                                                      |
//+------------------------------------------------------------------+
void UpdateSwingPoints()
{
    static datetime lastH1Bar = 0;
    datetime currentH1Bar = iTime(Symbol(), PERIOD_H1, 0);
    
    // 仅在新H1 bar时更新
    if(lastH1Bar != currentH1Bar)
    {
        int highestBar = iHighest(Symbol(), PERIOD_H1, MODE_HIGH, LookbackPeriod, 1);
        int lowestBar = iLowest(Symbol(), PERIOD_H1, MODE_LOW, LookbackPeriod, 1);
        
        if(highestBar != -1 && lowestBar != -1)
        {
            double newSwingHigh = iHigh(Symbol(), PERIOD_H1, highestBar);
            double newSwingLow = iLow(Symbol(), PERIOD_H1, lowestBar);
            
            if(newSwingHigh != SwingHigh || newSwingLow != SwingLow)
            {
                SwingHigh = newSwingHigh;
                SwingLow = newSwingLow;
                LastSwingHighBar = highestBar;
                LastSwingLowBar = lowestBar;
                
                Print("Swing点已更新 - High: ", SwingHigh, ", Low: ", SwingLow);
            }
        }
        
        lastH1Bar = currentH1Bar;
    }
}

//+------------------------------------------------------------------+
//| 检查信号冷却                                                      |
//+------------------------------------------------------------------+
bool IsSignalCooldownClear(ENUM_SIGNAL_TYPE signalType)
{
    if(CooldownBars <= 0)
        return true; // 冷却机制已禁用

    datetime currentTime = TimeCurrent();
    int cooldownSeconds = CooldownBars * 300; // M5 = 300秒

    if(signalType == SIGNAL_BUY)
    {
        // 检查是否在同一Swing Low区域
        if(MathAbs(SwingLow - LastSwingLowUsed) < (SLBuffer * PipSize))
        {
            if(currentTime - LastBuySignalTime < cooldownSeconds)
            {
                return false;
            }
        }
    }
    else if(signalType == SIGNAL_SELL)
    {
        // 检查是否在同一Swing High区域
        if(MathAbs(SwingHigh - LastSwingHighUsed) < (SLBuffer * PipSize))
        {
            if(currentTime - LastSellSignalTime < cooldownSeconds)
            {
                return false;
            }
        }
    }

    return true;
}

//+------------------------------------------------------------------+
//| 检查反转速度（做多信号）                                          |
//+------------------------------------------------------------------+
bool IsReversalSpeedAcceptableBuy(double wickLow, double currentClose)
{
    if(MinReversalSpeed <= 0)
        return true; // 反转速度过滤已禁用

    datetime currentBarTime = iTime(Symbol(), PERIOD_M5, 0);
    datetime currentTime = TimeCurrent();

    // 计算从bar开始到现在的时间差（秒）
    int timeDiff = (int)(currentTime - currentBarTime);
    if(timeDiff <= 0) timeDiff = 1; // 避免除零

    // 计算反转距离（pips）- 做多时从低点反转向上
    double reversalDistance = (currentClose - wickLow) / PipSize;
    if(reversalDistance <= 0) return false; // 没有向上反转

    // 计算反转速度（pips/秒）
    double reversalSpeed = reversalDistance / timeDiff;

    return (reversalSpeed >= MinReversalSpeed);
}

//+------------------------------------------------------------------+
//| 检查反转速度（做空信号）                                          |
//+------------------------------------------------------------------+
bool IsReversalSpeedAcceptableSell(double wickHigh, double currentClose)
{
    if(MinReversalSpeed <= 0)
        return true; // 反转速度过滤已禁用

    datetime currentBarTime = iTime(Symbol(), PERIOD_M5, 0);
    datetime currentTime = TimeCurrent();

    // 计算从bar开始到现在的时间差（秒）
    int timeDiff = (int)(currentTime - currentBarTime);
    if(timeDiff <= 0) timeDiff = 1; // 避免除零

    // 计算反转距离（pips）- 做空时从高点反转向下
    double reversalDistance = (wickHigh - currentClose) / PipSize;
    if(reversalDistance <= 0) return false; // 没有向下反转

    // 计算反转速度（pips/秒）
    double reversalSpeed = reversalDistance / timeDiff;

    return (reversalSpeed >= MinReversalSpeed);
}

//+------------------------------------------------------------------+
//| 检查流动性抓取信号                                                |
//+------------------------------------------------------------------+
ENUM_SIGNAL_TYPE CheckLiquidityGrabSignal()
{
    // 检查波动率过滤
    if(!IsVolatilityAcceptable())
        return SIGNAL_NONE;

    // 获取当前M5蜡烛数据
    double currentHigh = iHigh(Symbol(), PERIOD_M5, 0);
    double currentLow = iLow(Symbol(), PERIOD_M5, 0);
    double currentClose = iClose(Symbol(), PERIOD_M5, 0);

    // 检查做多信号（Low Liquidity Grab）
    if(currentLow < SwingLow && currentClose > SwingLow)
    {
        double wickSize = (SwingLow - currentLow) / PipSize;

        if(wickSize >= MinWickSize)
        {
            // 检查反转速度（做多信号）
            if(!IsReversalSpeedAcceptableBuy(currentLow, currentClose))
                return SIGNAL_NONE;

            // 检查信号冷却
            if(!IsSignalCooldownClear(SIGNAL_BUY))
                return SIGNAL_NONE;

            // 检查H1趋势过滤
            if(TrendFilterEnabled && GetH1TrendDirection() < TREND_SIDEWAYS)
                return SIGNAL_NONE;

            // 检查M15趋势过滤
            if(M15TrendFilterEnabled)
            {
                ENUM_TREND_DIRECTION h1Trend = GetH1TrendDirection();
                ENUM_TREND_DIRECTION m15Trend = GetM15TrendDirection();

                // 要求H1和M15趋势一致或至少不相反
                if(h1Trend == TREND_DOWNTREND || m15Trend == TREND_DOWNTREND)
                    return SIGNAL_NONE;

                if(h1Trend != m15Trend && h1Trend != TREND_SIDEWAYS && m15Trend != TREND_SIDEWAYS)
                    return SIGNAL_NONE;
            }

            LogTradeEvent("SIGNAL_BUY", "检测到做多信号 - Wick大小: " + DoubleToString(wickSize, 1) + " pips");

            // 更新信号时间和使用的Swing点
            LastBuySignalTime = TimeCurrent();
            LastSwingLowUsed = SwingLow;

            return SIGNAL_BUY;
        }
    }

    // 检查做空信号（High Liquidity Grab）
    if(currentHigh > SwingHigh && currentClose < SwingHigh)
    {
        double wickSize = (currentHigh - SwingHigh) / PipSize;

        if(wickSize >= MinWickSize)
        {
            // 检查反转速度（做空信号）
            if(!IsReversalSpeedAcceptableSell(currentHigh, currentClose))
                return SIGNAL_NONE;

            // 检查信号冷却
            if(!IsSignalCooldownClear(SIGNAL_SELL))
                return SIGNAL_NONE;

            // 检查H1趋势过滤
            if(TrendFilterEnabled && GetH1TrendDirection() > TREND_SIDEWAYS)
                return SIGNAL_NONE;

            // 检查M15趋势过滤
            if(M15TrendFilterEnabled)
            {
                ENUM_TREND_DIRECTION h1Trend = GetH1TrendDirection();
                ENUM_TREND_DIRECTION m15Trend = GetM15TrendDirection();

                // 要求H1和M15趋势一致或至少不相反
                if(h1Trend == TREND_UPTREND || m15Trend == TREND_UPTREND)
                    return SIGNAL_NONE;

                if(h1Trend != m15Trend && h1Trend != TREND_SIDEWAYS && m15Trend != TREND_SIDEWAYS)
                    return SIGNAL_NONE;
            }

            LogTradeEvent("SIGNAL_SELL", "检测到做空信号 - Wick大小: " + DoubleToString(wickSize, 1) + " pips");

            // 更新信号时间和使用的Swing点
            LastSellSignalTime = TimeCurrent();
            LastSwingHighUsed = SwingHigh;

            return SIGNAL_SELL;
        }
    }

    return SIGNAL_NONE;
}

//+------------------------------------------------------------------+
//| 获取H1趋势方向                                                    |
//+------------------------------------------------------------------+
ENUM_TREND_DIRECTION GetH1TrendDirection()
{
    // 获取前两个Swing点进行比较
    int prevHighestBar = iHighest(Symbol(), PERIOD_H1, MODE_HIGH, LookbackPeriod, LastSwingHighBar + 1);
    int prevLowestBar = iLowest(Symbol(), PERIOD_H1, MODE_LOW, LookbackPeriod, LastSwingLowBar + 1);

    if(prevHighestBar == -1 || prevLowestBar == -1)
        return TREND_SIDEWAYS;

    double prevSwingHigh = iHigh(Symbol(), PERIOD_H1, prevHighestBar);
    double prevSwingLow = iLow(Symbol(), PERIOD_H1, prevLowestBar);

    // 判断趋势方向
    bool higherHigh = SwingHigh > prevSwingHigh;
    bool higherLow = SwingLow > prevSwingLow;
    bool lowerHigh = SwingHigh < prevSwingHigh;
    bool lowerLow = SwingLow < prevSwingLow;

    if(higherHigh && higherLow)
        return TREND_UPTREND;
    else if(lowerHigh && lowerLow)
        return TREND_DOWNTREND;
    else
        return TREND_SIDEWAYS;
}

//+------------------------------------------------------------------+
//| 获取M15趋势方向                                                   |
//+------------------------------------------------------------------+
ENUM_TREND_DIRECTION GetM15TrendDirection()
{
    int highestBar = iHighest(Symbol(), PERIOD_M15, MODE_HIGH, LookbackPeriod, 1);
    int lowestBar = iLowest(Symbol(), PERIOD_M15, MODE_LOW, LookbackPeriod, 1);

    if(highestBar == -1 || lowestBar == -1)
        return TREND_SIDEWAYS;

    double currentHigh = iHigh(Symbol(), PERIOD_M15, highestBar);
    double currentLow = iLow(Symbol(), PERIOD_M15, lowestBar);

    // 获取前一个周期的Swing点
    int prevHighestBar = iHighest(Symbol(), PERIOD_M15, MODE_HIGH, LookbackPeriod, highestBar + 1);
    int prevLowestBar = iLowest(Symbol(), PERIOD_M15, MODE_LOW, LookbackPeriod, lowestBar + 1);

    if(prevHighestBar == -1 || prevLowestBar == -1)
        return TREND_SIDEWAYS;

    double prevHigh = iHigh(Symbol(), PERIOD_M15, prevHighestBar);
    double prevLow = iLow(Symbol(), PERIOD_M15, prevLowestBar);

    // 判断M15趋势方向
    bool higherHigh = currentHigh > prevHigh;
    bool higherLow = currentLow > prevLow;
    bool lowerHigh = currentHigh < prevHigh;
    bool lowerLow = currentLow < prevLow;

    if(higherHigh && higherLow)
        return TREND_UPTREND;
    else if(lowerHigh && lowerLow)
        return TREND_DOWNTREND;
    else
        return TREND_SIDEWAYS;
}

//+------------------------------------------------------------------+
//| 检查波动率过滤                                                    |
//+------------------------------------------------------------------+
bool IsVolatilityAcceptable()
{
    if(MinVolatility <= 0)
        return true; // 波动率过滤已禁用

    if(ATRHandle == INVALID_HANDLE)
        return true; // ATR句柄无效，跳过过滤

    double atrBuffer[1];
    if(CopyBuffer(ATRHandle, 0, 0, 1, atrBuffer) <= 0)
        return true; // 数据获取失败，跳过过滤

    double atrPips = atrBuffer[0] / PipSize;

    return (atrPips >= MinVolatility);
}

//+------------------------------------------------------------------+
//| 获取当前ATR值（用于动态SL/TP）                                    |
//+------------------------------------------------------------------+
double GetCurrentATR()
{
    int atrHandle = iATR(Symbol(), PERIOD_M5, 14);
    if(atrHandle == INVALID_HANDLE)
        return 10.0; // 默认ATR值

    double atrBuffer[1];
    if(CopyBuffer(atrHandle, 0, 0, 1, atrBuffer) <= 0)
        return 10.0; // 默认ATR值

    return atrBuffer[0] / PipSize; // 返回pips值
}

//+------------------------------------------------------------------+
//| 执行订单                                                          |
//+------------------------------------------------------------------+
void ExecuteOrder(ENUM_SIGNAL_TYPE signal)
{
    // 计算仓位大小
    double lotSize = CalculatePositionSize(signal);
    if(lotSize <= 0)
    {
        Print("错误: 无法计算有效的仓位大小");
        return;
    }

    // 获取当前价格
    double ask = SymbolInfoDouble(Symbol(), SYMBOL_ASK);
    double bid = SymbolInfoDouble(Symbol(), SYMBOL_BID);

    double entryPrice, stopLoss, takeProfit;
    string comment = CommentPrefix;

    if(signal == SIGNAL_BUY)
    {
        entryPrice = ask;

        if(EnableATRMode)
        {
            // ATR动态模式
            double atrValue = GetCurrentATR();
            double slDistance = atrValue * ATRMultiplier * PipSize;
            stopLoss = entryPrice - slDistance;
            takeProfit = entryPrice + (slDistance * TPRatio);
            comment += "Buy_ATR";
        }
        else
        {
            // 固定Swing点模式
            stopLoss = SwingLow - (SLBuffer * PipSize);
            double slDistance = entryPrice - stopLoss;
            takeProfit = entryPrice + (slDistance * TPRatio);
            comment += "Buy_LiqGrab";
        }

        // 执行买入订单
        if(trade.Buy(lotSize, Symbol(), entryPrice, stopLoss, takeProfit, comment))
        {
            string orderInfo = "手数: " + DoubleToString(lotSize, 2) +
                              ", 入场: " + DoubleToString(entryPrice, 2) +
                              ", SL: " + DoubleToString(stopLoss, 2) +
                              ", TP: " + DoubleToString(takeProfit, 2);
            LogTradeEvent("ORDER_BUY_SUCCESS", "买入订单执行成功 - " + orderInfo);

            // 初始化持仓跟踪
            InitializePositionTracking(trade.ResultOrder());
            TradesToday++;
        }
        else
        {
            HandleOrderError("买入", trade.ResultRetcode(), trade.ResultRetcodeDescription());
        }
    }
    else if(signal == SIGNAL_SELL)
    {
        entryPrice = bid;

        if(EnableATRMode)
        {
            // ATR动态模式
            double atrValue = GetCurrentATR();
            double slDistance = atrValue * ATRMultiplier * PipSize;
            stopLoss = entryPrice + slDistance;
            takeProfit = entryPrice - (slDistance * TPRatio);
            comment += "Sell_ATR";
        }
        else
        {
            // 固定Swing点模式
            stopLoss = SwingHigh + (SLBuffer * PipSize);
            double slDistance = stopLoss - entryPrice;
            takeProfit = entryPrice - (slDistance * TPRatio);
            comment += "Sell_LiqGrab";
        }

        // 执行卖出订单
        if(trade.Sell(lotSize, Symbol(), entryPrice, stopLoss, takeProfit, comment))
        {
            string orderInfo = "手数: " + DoubleToString(lotSize, 2) +
                              ", 入场: " + DoubleToString(entryPrice, 2) +
                              ", SL: " + DoubleToString(stopLoss, 2) +
                              ", TP: " + DoubleToString(takeProfit, 2);
            LogTradeEvent("ORDER_SELL_SUCCESS", "卖出订单执行成功 - " + orderInfo);

            // 初始化持仓跟踪
            InitializePositionTracking(trade.ResultOrder());
            TradesToday++;
        }
        else
        {
            HandleOrderError("卖出", trade.ResultRetcode(), trade.ResultRetcodeDescription());
        }
    }
}

//+------------------------------------------------------------------+
//| 计算仓位大小                                                      |
//+------------------------------------------------------------------+
double CalculatePositionSize(ENUM_SIGNAL_TYPE signal)
{
    double accountBalance = AccountInfoDouble(ACCOUNT_BALANCE);
    double riskAmount = accountBalance * (RiskPerTrade / 100.0);

    double slDistance;

    if(signal == SIGNAL_BUY)
    {
        double entryPrice = SymbolInfoDouble(Symbol(), SYMBOL_ASK);
        double stopLoss = SwingLow - (SLBuffer * PipSize);
        slDistance = entryPrice - stopLoss;
    }
    else if(signal == SIGNAL_SELL)
    {
        double entryPrice = SymbolInfoDouble(Symbol(), SYMBOL_BID);
        double stopLoss = SwingHigh + (SLBuffer * PipSize);
        slDistance = stopLoss - entryPrice;
    }
    else
    {
        return 0.0;
    }

    if(slDistance <= 0)
    {
        Print("错误: 止损距离无效");
        return 0.0;
    }

    // 计算手数
    double lotSize = riskAmount / (slDistance * PointValue);

    // 标准化手数
    double minLot = SymbolInfoDouble(Symbol(), SYMBOL_VOLUME_MIN);
    double maxLot = SymbolInfoDouble(Symbol(), SYMBOL_VOLUME_MAX);
    double lotStep = SymbolInfoDouble(Symbol(), SYMBOL_VOLUME_STEP);

    lotSize = MathFloor(lotSize / lotStep) * lotStep;
    lotSize = MathMax(lotSize, minLot);
    lotSize = MathMin(lotSize, maxLot);

    return lotSize;
}

//+------------------------------------------------------------------+
//| 管理现有订单                                                      |
//+------------------------------------------------------------------+
void ManageOrders()
{
    // 检查是否有持仓需要管理
    for(int i = PositionsTotal() - 1; i >= 0; i--)
    {
        if(PositionGetTicket(i))
        {
            if(PositionGetInteger(POSITION_MAGIC) == MagicNumber &&
               PositionGetString(POSITION_SYMBOL) == Symbol())
            {
                ulong ticket = PositionGetTicket(i);
                ENUM_POSITION_TYPE posType = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE);
                double openPrice = PositionGetDouble(POSITION_PRICE_OPEN);
                double currentPrice = (posType == POSITION_TYPE_BUY) ?
                                    SymbolInfoDouble(Symbol(), SYMBOL_BID) :
                                    SymbolInfoDouble(Symbol(), SYMBOL_ASK);
                datetime openTime = (datetime)PositionGetInteger(POSITION_TIME);

                // 检查持仓时间限制
                if(MaxHoldingMinutes > 0)
                {
                    int holdingMinutes = (int)((TimeCurrent() - openTime) / 60);
                    if(holdingMinutes >= MaxHoldingMinutes)
                    {
                        trade.PositionClose(ticket);
                        LogTradeEvent("FORCE_CLOSE_TIME", "持仓时间超限，强制平仓订单: " + IntegerToString(ticket));
                        continue;
                    }
                }

                // 检查是否需要强制平仓（时段结束）
                if(!IsInTradingHours())
                {
                    trade.PositionClose(ticket);
                    LogTradeEvent("FORCE_CLOSE_SESSION", "时段结束，强制平仓订单: " + IntegerToString(ticket));
                    continue;
                }

                // 检查市场结构变化强制退出
                if(CheckForceExitCondition(posType, openPrice))
                {
                    trade.PositionClose(ticket);
                    LogTradeEvent("FORCE_CLOSE_STRUCTURE", "市场结构变化，强制平仓订单: " + IntegerToString(ticket));
                    continue;
                }

                // 检查部分平仓
                if(EnablePartialClose)
                {
                    CheckPartialClose(ticket, posType, openPrice, currentPrice);
                }

                // 检查追踪止损
                if(EnableTrailingStop)
                {
                    CheckTrailingStop(ticket, posType, openPrice, currentPrice);
                }
            }
        }
    }

    // 清理已关闭持仓的跟踪记录
    CleanupClosedPositions();

    // 检查已完成的订单，更新统计
    UpdateTradeStatistics();
}

//+------------------------------------------------------------------+
//| 检查部分平仓                                                      |
//+------------------------------------------------------------------+
void CheckPartialClose(ulong ticket, ENUM_POSITION_TYPE posType, double openPrice, double currentPrice)
{
    // 检查是否已经执行过部分平仓
    int posIndex = GetPositionTrackerIndex(ticket);
    if(posIndex >= 0 && posIndex < ArraySize(positionTrackers) &&
       positionTrackers[posIndex].partialCloseExecuted)
        return;

    double profit = 0.0;
    if(posType == POSITION_TYPE_BUY)
        profit = (currentPrice - openPrice) / PipSize;
    else
        profit = (openPrice - currentPrice) / PipSize;

    // 计算止损距离
    double slDistance = 0.0;
    if(posType == POSITION_TYPE_BUY)
        slDistance = (openPrice - (SwingLow - SLBuffer * PipSize)) / PipSize;
    else
        slDistance = ((SwingHigh + SLBuffer * PipSize) - openPrice) / PipSize;

    // 检查是否达到1:1风险回报比
    if(profit >= slDistance)
    {
        double currentVolume = PositionGetDouble(POSITION_VOLUME);
        double partialVolume = currentVolume * PartialCloseRatio;

        // 标准化手数
        double minLot = SymbolInfoDouble(Symbol(), SYMBOL_VOLUME_MIN);
        double lotStep = SymbolInfoDouble(Symbol(), SYMBOL_VOLUME_STEP);
        partialVolume = MathFloor(partialVolume / lotStep) * lotStep;
        partialVolume = MathMax(partialVolume, minLot);

        if(partialVolume < currentVolume)
        {
            if(trade.PositionClosePartial(ticket, partialVolume))
            {
                // 标记已执行部分平仓
                if(posIndex >= 0 && posIndex < ArraySize(positionTrackers))
                    positionTrackers[posIndex].partialCloseExecuted = true;

                LogTradeEvent("PARTIAL_CLOSE", "部分平仓执行 - 订单: " + IntegerToString(ticket) +
                             ", 手数: " + DoubleToString(partialVolume, 2));
            }
        }
    }
}

//+------------------------------------------------------------------+
//| 检查追踪止损                                                      |
//+------------------------------------------------------------------+
void CheckTrailingStop(ulong ticket, ENUM_POSITION_TYPE posType, double openPrice, double currentPrice)
{
    double profit = 0.0;
    if(posType == POSITION_TYPE_BUY)
        profit = (currentPrice - openPrice) / PipSize;
    else
        profit = (openPrice - currentPrice) / PipSize;

    // 检查是否达到追踪止损触发条件
    if(profit >= TrailingTrigger)
    {
        int posIndex = GetPositionTrackerIndex(ticket);
        bool needActivate = true;

        if(posIndex >= 0 && posIndex < ArraySize(positionTrackers))
            needActivate = !positionTrackers[posIndex].trailingStopActivated;

        if(needActivate)
        {
            // 激活追踪止损，将止损移动到盈亏平衡点
            double newSL = openPrice;

            if(trade.PositionModify(ticket, newSL, PositionGetDouble(POSITION_TP)))
            {
                if(posIndex >= 0 && posIndex < ArraySize(positionTrackers))
                    positionTrackers[posIndex].trailingStopActivated = true;

                LogTradeEvent("TRAILING_STOP", "追踪止损激活 - 订单: " + IntegerToString(ticket) +
                             ", 新止损: " + DoubleToString(newSL, 2));
            }
        }
    }
}

//+------------------------------------------------------------------+
//| 获取持仓跟踪索引                                                  |
//+------------------------------------------------------------------+
int GetPositionTrackerIndex(ulong ticket)
{
    // 先查找是否已存在
    for(int i = 0; i < ArraySize(positionTrackers); i++)
    {
        if(positionTrackers[i].ticket == ticket)
            return i;
    }

    // 查找空位
    for(int i = 0; i < ArraySize(positionTrackers); i++)
    {
        if(positionTrackers[i].ticket == 0)
            return i;
    }

    // 扩展数组
    int oldSize = ArraySize(positionTrackers);
    ArrayResize(positionTrackers, oldSize + 10);

    return oldSize;
}

//+------------------------------------------------------------------+
//| 初始化持仓跟踪                                                    |
//+------------------------------------------------------------------+
void InitializePositionTracking(ulong orderTicket)
{
    int index = GetPositionTrackerIndex(orderTicket);
    if(index >= 0 && index < ArraySize(positionTrackers))
    {
        positionTrackers[index].ticket = orderTicket;
        positionTrackers[index].openTime = TimeCurrent();
        positionTrackers[index].partialCloseExecuted = false;
        positionTrackers[index].trailingStopActivated = false;
    }
}

//+------------------------------------------------------------------+
//| 清理已关闭持仓的跟踪记录                                          |
//+------------------------------------------------------------------+
void CleanupClosedPositions()
{
    for(int i = 0; i < ArraySize(positionTrackers); i++)
    {
        if(positionTrackers[i].ticket != 0)
        {
            // 检查持仓是否还存在
            bool positionExists = false;
            for(int j = 0; j < PositionsTotal(); j++)
            {
                if(PositionGetTicket(j) == positionTrackers[i].ticket)
                {
                    positionExists = true;
                    break;
                }
            }

            // 如果持仓不存在，清理跟踪记录
            if(!positionExists)
            {
                positionTrackers[i].ticket = 0;
                positionTrackers[i].openTime = 0;
                positionTrackers[i].partialCloseExecuted = false;
                positionTrackers[i].trailingStopActivated = false;
            }
        }
    }
}

//+------------------------------------------------------------------+
//| 更新交易统计                                                      |
//+------------------------------------------------------------------+
void UpdateTradeStatistics()
{
    static int lastDealsTotal = 0;
    int currentDealsTotal = HistoryDealsTotal();

    if(currentDealsTotal > lastDealsTotal)
    {
        // 检查最新的交易结果
        for(int i = lastDealsTotal; i < currentDealsTotal; i++)
        {
            ulong dealTicket = HistoryDealGetTicket(i);
            if(dealTicket > 0)
            {
                if(HistoryDealGetInteger(dealTicket, DEAL_MAGIC) == MagicNumber &&
                   HistoryDealGetString(dealTicket, DEAL_SYMBOL) == Symbol())
                {
                    ENUM_DEAL_TYPE dealType = (ENUM_DEAL_TYPE)HistoryDealGetInteger(dealTicket, DEAL_TYPE);

                    if(dealType == DEAL_TYPE_BUY || dealType == DEAL_TYPE_SELL)
                    {
                        // 这是开仓交易，跳过
                        continue;
                    }

                    double profit = HistoryDealGetDouble(dealTicket, DEAL_PROFIT);

                    if(profit < 0)
                    {
                        ConsecutiveLosses++;
                        Print("连续亏损计数: ", ConsecutiveLosses);

                        if(ConsecutiveLosses >= MaxConsecutiveLosses)
                        {
                            EAActive = false;
                            Print("达到最大连续亏损数，EA暂停交易");
                        }
                    }
                    else if(profit > 0)
                    {
                        ConsecutiveLosses = 0; // 重置连续亏损计数
                        Print("盈利交易，连续亏损计数重置");
                    }
                }
            }
        }

        lastDealsTotal = currentDealsTotal;
    }
}

//+------------------------------------------------------------------+
//| 获取EA状态信息                                                    |
//+------------------------------------------------------------------+
string GetEAStatus()
{
    string status = "XAUUSD流动性抓取EA状态 v2.0:\n";
    status += "EA激活: " + (EAActive ? "是" : "否") + "\n";
    status += "今日交易数: " + IntegerToString(TradesToday) + "/" + IntegerToString(MaxTradesPerDay) + "\n";
    status += "连续亏损: " + IntegerToString(ConsecutiveLosses) + "/" + IntegerToString(MaxConsecutiveLosses) + "\n";
    status += "当前Swing High: " + DoubleToString(SwingHigh, 2) + "\n";
    status += "当前Swing Low: " + DoubleToString(SwingLow, 2) + "\n";
    status += "交易时段: " + (IsInTradingHours() ? "是" : "否") + "\n";
    status += "点差可接受: " + (IsSpreadAcceptable() ? "是" : "否") + "\n";
    status += "波动率可接受: " + (IsVolatilityAcceptable() ? "是" : "否") + "\n";
    status += "新闻过滤: " + (NewsFilterActive ? "激活" : "正常") + "\n";

    // 显示趋势状态
    ENUM_TREND_DIRECTION h1Trend = GetH1TrendDirection();
    ENUM_TREND_DIRECTION m15Trend = GetM15TrendDirection();
    string h1TrendStr = (h1Trend == TREND_UPTREND) ? "上涨" : (h1Trend == TREND_DOWNTREND) ? "下跌" : "横盘";
    string m15TrendStr = (m15Trend == TREND_UPTREND) ? "上涨" : (m15Trend == TREND_DOWNTREND) ? "下跌" : "横盘";
    status += "H1趋势: " + h1TrendStr + " | M15趋势: " + m15TrendStr + "\n";

    // 显示持仓信息
    int totalPositions = PositionsTotal();
    status += "当前持仓: " + IntegerToString(totalPositions) + "\n";

    return status;
}

//+------------------------------------------------------------------+
//| 处理订单错误                                                      |
//+------------------------------------------------------------------+
void HandleOrderError(string orderType, uint errorCode, string errorDescription)
{
    Print("订单执行失败 - 类型: ", orderType, ", 错误代码: ", errorCode, ", 描述: ", errorDescription);

    // 根据错误类型进行不同处理
    switch(errorCode)
    {
        case TRADE_RETCODE_REQUOTE:
        case TRADE_RETCODE_PRICE_OFF:
        case TRADE_RETCODE_PRICE_CHANGED:
            Print("价格变化，等待下一个信号");
            break;

        case TRADE_RETCODE_INVALID_VOLUME:
        case TRADE_RETCODE_INVALID_PRICE:
            Print("参数错误，检查交易设置");
            break;

        case TRADE_RETCODE_NO_MONEY:
            Print("资金不足，暂停交易");
            EAActive = false;
            break;

        case TRADE_RETCODE_TRADE_DISABLED:
        case TRADE_RETCODE_MARKET_CLOSED:
            Print("市场关闭或交易被禁用");
            break;

        case TRADE_RETCODE_CONNECTION:
        case TRADE_RETCODE_TIMEOUT:
            Print("连接问题，稍后重试");
            break;

        default:
            Print("未知错误，记录详细信息");
            break;
    }

    // 记录错误到文件（可选）
    LogErrorToFile(orderType, errorCode, errorDescription);
}

//+------------------------------------------------------------------+
//| 记录错误到文件                                                    |
//+------------------------------------------------------------------+
void LogErrorToFile(string orderType, uint errorCode, string errorDescription)
{
    string fileName = "XAUUSD_LiqGrab_Errors_" + TimeToString(TimeCurrent(), TIME_DATE) + ".log";
    int fileHandle = FileOpen(fileName, FILE_WRITE|FILE_READ|FILE_TXT|FILE_ANSI, '\t');

    if(fileHandle != INVALID_HANDLE)
    {
        // 移动到文件末尾以追加内容
        FileSeek(fileHandle, 0, SEEK_END);

        string logEntry = TimeToString(TimeCurrent()) + "\t" +
                         orderType + "\t" +
                         IntegerToString(errorCode) + "\t" +
                         errorDescription + "\n";
        FileWrite(fileHandle, logEntry);
        FileClose(fileHandle);
    }
}

//+------------------------------------------------------------------+
//| 检查数据完整性                                                    |
//+------------------------------------------------------------------+
bool CheckDataIntegrity()
{
    // 检查历史数据是否可用
    if(iBars(Symbol(), PERIOD_M5) < LookbackPeriod + 10)
    {
        Print("警告: M5历史数据不足");
        return false;
    }

    if(iBars(Symbol(), PERIOD_H1) < LookbackPeriod + 10)
    {
        Print("警告: H1历史数据不足");
        return false;
    }

    // 检查当前价格数据
    double ask = SymbolInfoDouble(Symbol(), SYMBOL_ASK);
    double bid = SymbolInfoDouble(Symbol(), SYMBOL_BID);

    if(ask <= 0 || bid <= 0 || ask <= bid)
    {
        Print("错误: 价格数据异常 - Ask: ", ask, ", Bid: ", bid);
        return false;
    }

    return true;
}

//+------------------------------------------------------------------+
//| 检查强制退出条件                                                  |
//+------------------------------------------------------------------+
bool CheckForceExitCondition(ENUM_POSITION_TYPE posType, double openPrice)
{
    // 获取当前M5蜡烛数据
    double currentHigh = iHigh(Symbol(), PERIOD_M5, 0);
    double currentLow = iLow(Symbol(), PERIOD_M5, 0);
    double currentClose = iClose(Symbol(), PERIOD_M5, 0);

    // 检查是否形成了反转结构
    if(posType == POSITION_TYPE_BUY)
    {
        // 对于多头持仓，检查是否出现新的低点突破
        if(currentLow < SwingLow - (SLBuffer * PipSize))
        {
            Print("多头持仓检测到反转结构，当前低点: ", currentLow, ", Swing Low: ", SwingLow);
            return true;
        }

        // 检查是否出现强烈的反转信号（大阴线）
        if(currentClose < currentHigh - (MinWickSize * 2 * PipSize) &&
           currentClose < openPrice)
        {
            Print("多头持仓检测到强烈反转信号");
            return true;
        }
    }
    else if(posType == POSITION_TYPE_SELL)
    {
        // 对于空头持仓，检查是否出现新的高点突破
        if(currentHigh > SwingHigh + (SLBuffer * PipSize))
        {
            Print("空头持仓检测到反转结构，当前高点: ", currentHigh, ", Swing High: ", SwingHigh);
            return true;
        }

        // 检查是否出现强烈的反转信号（大阳线）
        if(currentClose > currentLow + (MinWickSize * 2 * PipSize) &&
           currentClose > openPrice)
        {
            Print("空头持仓检测到强烈反转信号");
            return true;
        }
    }

    return false;
}

//+------------------------------------------------------------------+
//| 定时器中更新Swing点                                              |
//+------------------------------------------------------------------+
void UpdateSwingPointsTimer()
{
    int highestBar = iHighest(Symbol(), PERIOD_H1, MODE_HIGH, LookbackPeriod, 1);
    int lowestBar = iLowest(Symbol(), PERIOD_H1, MODE_LOW, LookbackPeriod, 1);

    if(highestBar != -1 && lowestBar != -1)
    {
        double newSwingHigh = iHigh(Symbol(), PERIOD_H1, highestBar);
        double newSwingLow = iLow(Symbol(), PERIOD_H1, lowestBar);

        if(newSwingHigh != SwingHigh || newSwingLow != SwingLow)
        {
            SwingHigh = newSwingHigh;
            SwingLow = newSwingLow;
            LastSwingHighBar = highestBar;
            LastSwingLowBar = lowestBar;

            LogTradeEvent("SWING_UPDATE", "Swing点更新 - High: " + DoubleToString(SwingHigh, 2) +
                         ", Low: " + DoubleToString(SwingLow, 2));
        }
    }
}

//+------------------------------------------------------------------+
//| 检查EA健康状态                                                   |
//+------------------------------------------------------------------+
void CheckEAHealth()
{
    // 检查内存使用
    long memoryUsed = TerminalInfoInteger(TERMINAL_MEMORY_USED);
    if(memoryUsed > 100) // 超过100MB
    {
        Print("警告: 内存使用过高 - ", memoryUsed, " MB");
    }

    // 检查连接状态
    if(!TerminalInfoInteger(TERMINAL_CONNECTED))
    {
        Print("警告: 终端连接断开");
        EAActive = false;
    }

    // 检查交易权限
    if(!TerminalInfoInteger(TERMINAL_TRADE_ALLOWED))
    {
        Print("警告: 交易权限被禁用");
        EAActive = false;
    }
}

//+------------------------------------------------------------------+
//| 清理旧日志文件                                                    |
//+------------------------------------------------------------------+
void CleanupOldLogs()
{
    // 删除7天前的日志文件
    datetime cutoffTime = TimeCurrent() - (7 * 24 * 3600);
    MqlDateTime cutoffStruct;
    TimeToStruct(cutoffTime, cutoffStruct);

    string cutoffDate = StringFormat("%04d.%02d.%02d",
                                    cutoffStruct.year,
                                    cutoffStruct.mon,
                                    cutoffStruct.day);

    // 这里可以添加文件删除逻辑（需要文件操作权限）
    Print("日志清理检查完成，截止日期: ", cutoffDate);
}

//+------------------------------------------------------------------+
//| 记录交易事件                                                      |
//+------------------------------------------------------------------+
void LogTradeEvent(string eventType, string eventDescription)
{
    string fileName = "XAUUSD_LiqGrab_Events_" + TimeToString(TimeCurrent(), TIME_DATE) + ".log";
    int fileHandle = FileOpen(fileName, FILE_WRITE|FILE_READ|FILE_TXT|FILE_ANSI, '\t');

    if(fileHandle != INVALID_HANDLE)
    {
        // 移动到文件末尾以追加内容
        FileSeek(fileHandle, 0, SEEK_END);

        string logEntry = TimeToString(TimeCurrent()) + "\t" +
                         eventType + "\t" +
                         eventDescription + "\n";
        FileWrite(fileHandle, logEntry);
        FileClose(fileHandle);
    }

    // 同时输出到终端
    Print("[", eventType, "] ", eventDescription);
}

//+------------------------------------------------------------------+
//| 专家顾问注释函数                                                  |
//+------------------------------------------------------------------+
void OnComment()
{
    Comment(GetEAStatus());
}
