//+------------------------------------------------------------------+
//|                                           GoldProfessional.mq5 |
//|                                    黄金专业版策略 with 追踪止损 |
//|                                                                  |
//+------------------------------------------------------------------+
#property copyright "Gold Professional Strategy"
#property link      ""
#property version   "1.00"

//--- 输入参数
input group "=== 基础设置 ==="
input int                InpMagicNumber = 12345;           // 魔术数字
input double             InpRiskPercent = 0.5;             // 风险百分比 (%)
input ENUM_TIMEFRAMES    InpTimeframe = PERIOD_M1;         // 交易时间周期

input group "=== 55MA 通道设置 ==="
input int                InpMA55Period = 55;               // 55MA 周期
input ENUM_MA_METHOD     InpMAMethod = MODE_SMA;           // MA 计算方法

input group "=== ATR 设置 ==="
input int                InpATRPeriod = 14;                // ATR 周期
input double             InpATRMultiplier = 2.5;           // ATR 止损乘数

input group "=== HTF 过滤设置 ==="
input bool               InpUseHTFFilter = true;           // 启用HTF 200MA过滤
input ENUM_TIMEFRAMES    InpHTFTimeframe = PERIOD_M15;     // HTF 时间周期
input int                InpHTF_MA_Period = 200;           // HTF MA 周期

input group "=== 止盈止损设置 ==="
input double             InpRiskRewardRatio = 1.5;         // 盈亏比
input bool               InpUseTrailingStop = true;        // 启用追踪止损
input double             InpTrailingTrigger = 1.5;         // 追踪止损触发比例
input double             InpTrailingStep = 0.5;            // 追踪止损步长(ATR倍数)
input bool               InpPartialClose = false;          // 部分平仓选项

input group "=== 止损类型 ==="
enum ENUM_SL_TYPE
{
    SL_PREVIOUS_CANDLE,    // 前一根K线高低点
    SL_ATR_BASED          // 基于ATR
};
input ENUM_SL_TYPE InpStopLossType = SL_ATR_BASED;

//--- 全局变量
int handleMA55High, handleMA55Low, handleATR, handleHTF_MA;
int handleHeikenAshi;
double ma55High[], ma55Low[], atrValues[], htfMA[];
double heikenOpen[], heikenHigh[], heikenLow[], heikenClose[];

struct TradeInfo
{
    ulong ticket;
    double entryPrice;
    double initialSL;
    double currentSL;
    bool trailingActivated;
    datetime entryTime;
};

TradeInfo currentTrades[];

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    // 初始化指标句柄
    handleMA55High = iMA(_Symbol, InpTimeframe, InpMA55Period, 0, InpMAMethod, PRICE_HIGH);
    handleMA55Low = iMA(_Symbol, InpTimeframe, InpMA55Period, 0, InpMAMethod, PRICE_LOW);
    handleATR = iATR(_Symbol, InpTimeframe, InpATRPeriod);
    
    if(InpUseHTFFilter)
        handleHTF_MA = iMA(_Symbol, InpHTFTimeframe, InpHTF_MA_Period, 0, InpMAMethod, PRICE_CLOSE);
    
    // 初始化Heiken Ashi (使用自定义计算)
    
    // 检查句柄
    if(handleMA55High == INVALID_HANDLE || handleMA55Low == INVALID_HANDLE || 
       handleATR == INVALID_HANDLE || (InpUseHTFFilter && handleHTF_MA == INVALID_HANDLE))
    {
        Print("指标初始化失败");
        return(INIT_FAILED);
    }
    
    // 设置数组
    ArraySetAsSeries(ma55High, true);
    ArraySetAsSeries(ma55Low, true);
    ArraySetAsSeries(atrValues, true);
    ArraySetAsSeries(htfMA, true);
    ArraySetAsSeries(heikenOpen, true);
    ArraySetAsSeries(heikenHigh, true);
    ArraySetAsSeries(heikenLow, true);
    ArraySetAsSeries(heikenClose, true);
    
    Print("黄金专业版策略初始化成功");
    return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                               |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    // 释放指标句柄
    if(handleMA55High != INVALID_HANDLE) IndicatorRelease(handleMA55High);
    if(handleMA55Low != INVALID_HANDLE) IndicatorRelease(handleMA55Low);
    if(handleATR != INVALID_HANDLE) IndicatorRelease(handleATR);
    if(handleHTF_MA != INVALID_HANDLE) IndicatorRelease(handleHTF_MA);
    
    // 清理图表对象
    ObjectsDeleteAll(0, "GoldPro_");
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    // 检查新K线
    static datetime lastBarTime = 0;
    datetime currentBarTime = iTime(_Symbol, InpTimeframe, 0);
    
    if(currentBarTime == lastBarTime)
        return;
    lastBarTime = currentBarTime;
    
    // 更新指标数据
    if(!UpdateIndicators())
        return;
    
    // 管理现有交易
    ManageOpenTrades();
    
    // 检查新的交易信号
    CheckTradeSignals();
    
    // 更新可视化
    UpdateVisualization();
}

//+------------------------------------------------------------------+
//| 更新指标数据                                                     |
//+------------------------------------------------------------------+
bool UpdateIndicators()
{
    // 获取MA数据
    if(CopyBuffer(handleMA55High, 0, 0, 3, ma55High) <= 0 ||
       CopyBuffer(handleMA55Low, 0, 0, 3, ma55Low) <= 0 ||
       CopyBuffer(handleATR, 0, 0, 3, atrValues) <= 0)
    {
        return false;
    }
    
    // 获取HTF MA数据
    if(InpUseHTFFilter)
    {
        if(CopyBuffer(handleHTF_MA, 0, 0, 2, htfMA) <= 0)
            return false;
    }
    
    // 计算Heiken Ashi
    CalculateHeikenAshi();
    
    return true;
}

//+------------------------------------------------------------------+
//| 计算Heiken Ashi                                                 |
//+------------------------------------------------------------------+
void CalculateHeikenAshi()
{
    MqlRates rates[];
    ArraySetAsSeries(rates, true);
    
    if(CopyRates(_Symbol, InpTimeframe, 0, 10, rates) <= 0)
        return;
    
    ArrayResize(heikenOpen, 10);
    ArrayResize(heikenHigh, 10);
    ArrayResize(heikenLow, 10);
    ArrayResize(heikenClose, 10);
    
    for(int i = 9; i >= 0; i--)
    {
        // Heiken Ashi Close
        heikenClose[i] = (rates[i].open + rates[i].high + rates[i].low + rates[i].close) / 4.0;
        
        // Heiken Ashi Open
        if(i == 9)
            heikenOpen[i] = (rates[i].open + rates[i].close) / 2.0;
        else
            heikenOpen[i] = (heikenOpen[i+1] + heikenClose[i+1]) / 2.0;
        
        // Heiken Ashi High and Low
        heikenHigh[i] = MathMax(rates[i].high, MathMax(heikenOpen[i], heikenClose[i]));
        heikenLow[i] = MathMin(rates[i].low, MathMin(heikenOpen[i], heikenClose[i]));
    }
}

//+------------------------------------------------------------------+
//| 检查交易信号                                                     |
//+------------------------------------------------------------------+
void CheckTradeSignals()
{
    // 检查是否已有持仓
    if(PositionsTotal() > 0)
        return;

    double currentPrice = SymbolInfoDouble(_Symbol, SYMBOL_BID);

    // 检查价格是否在通道内（不交易区域）
    if(currentPrice > ma55Low[0] && currentPrice < ma55High[0])
        return;

    // HTF过滤
    if(InpUseHTFFilter)
    {
        double htfMAValue = htfMA[0];
        // 如果价格在HTF MA附近，不交易
        if(MathAbs(currentPrice - htfMAValue) < atrValues[0] * 0.5)
            return;
    }

    // 检查买入信号
    if(CheckBuySignal())
    {
        OpenBuyTrade();
    }
    // 检查卖出信号
    else if(CheckSellSignal())
    {
        OpenSellTrade();
    }
}

//+------------------------------------------------------------------+
//| 检查买入信号                                                     |
//+------------------------------------------------------------------+
bool CheckBuySignal()
{
    double currentPrice = SymbolInfoDouble(_Symbol, SYMBOL_ASK);

    // 条件1: Heiken Ashi为绿色（收盘价 > 开盘价）
    bool heikenBullish = heikenClose[0] > heikenOpen[0];

    // 条件2: 价格突破55MA高点
    bool priceAboveMA = currentPrice > ma55High[0];

    // 条件3: HTF过滤（可选）
    bool htfFilter = true;
    if(InpUseHTFFilter)
    {
        htfFilter = currentPrice > htfMA[0];
    }

    return heikenBullish && priceAboveMA && htfFilter;
}

//+------------------------------------------------------------------+
//| 检查卖出信号                                                     |
//+------------------------------------------------------------------+
bool CheckSellSignal()
{
    double currentPrice = SymbolInfoDouble(_Symbol, SYMBOL_BID);

    // 条件1: Heiken Ashi为红色（收盘价 < 开盘价）
    bool heikenBearish = heikenClose[0] < heikenOpen[0];

    // 条件2: 价格跌破55MA低点
    bool priceBelowMA = currentPrice < ma55Low[0];

    // 条件3: HTF过滤（可选）
    bool htfFilter = true;
    if(InpUseHTFFilter)
    {
        htfFilter = currentPrice < htfMA[0];
    }

    return heikenBearish && priceBelowMA && htfFilter;
}

//+------------------------------------------------------------------+
//| 开买单                                                           |
//+------------------------------------------------------------------+
void OpenBuyTrade()
{
    double price = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
    double sl = CalculateStopLoss(ORDER_TYPE_BUY, price);
    double tp = price + (price - sl) * InpRiskRewardRatio;

    double lotSize = CalculateLotSize(price - sl);

    MqlTradeRequest request = {};
    MqlTradeResult result = {};

    request.action = TRADE_ACTION_DEAL;
    request.symbol = _Symbol;
    request.volume = lotSize;
    request.type = ORDER_TYPE_BUY;
    request.price = price;
    request.sl = sl;
    request.tp = tp;
    request.magic = InpMagicNumber;
    request.comment = "GoldPro Buy";

    if(OrderSend(request, result))
    {
        // 添加到交易跟踪数组
        AddTradeToTracking(result.order, price, sl);
        Print("买单开仓成功: ", result.order);
    }
}

//+------------------------------------------------------------------+
//| 开卖单                                                           |
//+------------------------------------------------------------------+
void OpenSellTrade()
{
    double price = SymbolInfoDouble(_Symbol, SYMBOL_BID);
    double sl = CalculateStopLoss(ORDER_TYPE_SELL, price);
    double tp = price - (sl - price) * InpRiskRewardRatio;

    double lotSize = CalculateLotSize(sl - price);

    MqlTradeRequest request = {};
    MqlTradeResult result = {};

    request.action = TRADE_ACTION_DEAL;
    request.symbol = _Symbol;
    request.volume = lotSize;
    request.type = ORDER_TYPE_SELL;
    request.price = price;
    request.sl = sl;
    request.tp = tp;
    request.magic = InpMagicNumber;
    request.comment = "GoldPro Sell";

    if(OrderSend(request, result))
    {
        // 添加到交易跟踪数组
        AddTradeToTracking(result.order, price, sl);
        Print("卖单开仓成功: ", result.order);
    }
}

//+------------------------------------------------------------------+
//| 计算止损位                                                       |
//+------------------------------------------------------------------+
double CalculateStopLoss(ENUM_ORDER_TYPE orderType, double entryPrice)
{
    double sl = 0;

    if(InpStopLossType == SL_PREVIOUS_CANDLE)
    {
        MqlRates rates[];
        ArraySetAsSeries(rates, true);
        if(CopyRates(_Symbol, InpTimeframe, 0, 2, rates) > 0)
        {
            if(orderType == ORDER_TYPE_BUY)
                sl = rates[1].low;
            else
                sl = rates[1].high;
        }
    }
    else // SL_ATR_BASED
    {
        double atr = atrValues[0];
        if(orderType == ORDER_TYPE_BUY)
            sl = entryPrice - atr * InpATRMultiplier;
        else
            sl = entryPrice + atr * InpATRMultiplier;
    }

    return NormalizeDouble(sl, _Digits);
}

//+------------------------------------------------------------------+
//| 计算仓位大小                                                     |
//+------------------------------------------------------------------+
double CalculateLotSize(double riskDistance)
{
    double accountBalance = AccountInfoDouble(ACCOUNT_BALANCE);
    double riskAmount = accountBalance * InpRiskPercent / 100.0;

    double tickValue = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_VALUE);
    double tickSize = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_SIZE);

    double lotSize = riskAmount / (riskDistance / tickSize * tickValue);

    double minLot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN);
    double maxLot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MAX);
    double lotStep = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_STEP);

    lotSize = MathMax(minLot, MathMin(maxLot, lotSize));
    lotSize = NormalizeDouble(lotSize / lotStep, 0) * lotStep;

    return lotSize;
}

//+------------------------------------------------------------------+
//| 添加交易到跟踪数组                                               |
//+------------------------------------------------------------------+
void AddTradeToTracking(ulong ticket, double entryPrice, double initialSL)
{
    int size = ArraySize(currentTrades);
    ArrayResize(currentTrades, size + 1);

    currentTrades[size].ticket = ticket;
    currentTrades[size].entryPrice = entryPrice;
    currentTrades[size].initialSL = initialSL;
    currentTrades[size].currentSL = initialSL;
    currentTrades[size].trailingActivated = false;
    currentTrades[size].entryTime = TimeCurrent();
}

//+------------------------------------------------------------------+
//| 管理开仓交易                                                     |
//+------------------------------------------------------------------+
void ManageOpenTrades()
{
    for(int i = ArraySize(currentTrades) - 1; i >= 0; i--)
    {
        if(!PositionSelectByTicket(currentTrades[i].ticket))
        {
            // 交易已关闭，从数组中移除
            RemoveTradeFromTracking(i);
            continue;
        }

        // 检查趋势反转出场
        if(CheckTrendReversalExit(currentTrades[i].ticket))
        {
            ClosePosition(currentTrades[i].ticket);
            continue;
        }

        // 追踪止损管理
        if(InpUseTrailingStop)
        {
            ManageTrailingStop(i);
        }
    }
}

//+------------------------------------------------------------------+
//| 检查趋势反转出场                                                 |
//+------------------------------------------------------------------+
bool CheckTrendReversalExit(ulong ticket)
{
    if(!PositionSelectByTicket(ticket))
        return false;

    ENUM_POSITION_TYPE posType = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE);

    if(posType == POSITION_TYPE_BUY)
    {
        // 做多持仓：Heiken Ashi变红色
        return heikenClose[0] < heikenOpen[0];
    }
    else if(posType == POSITION_TYPE_SELL)
    {
        // 做空持仓：Heiken Ashi变绿色
        return heikenClose[0] > heikenOpen[0];
    }

    return false;
}

//+------------------------------------------------------------------+
//| 追踪止损管理                                                     |
//+------------------------------------------------------------------+
void ManageTrailingStop(int tradeIndex)
{
    ulong ticket = currentTrades[tradeIndex].ticket;

    if(!PositionSelectByTicket(ticket))
        return;

    double entryPrice = currentTrades[tradeIndex].entryPrice;
    double currentPrice = PositionGetDouble(POSITION_PRICE_CURRENT);
    ENUM_POSITION_TYPE posType = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE);

    double profit = 0;
    if(posType == POSITION_TYPE_BUY)
        profit = currentPrice - entryPrice;
    else
        profit = entryPrice - currentPrice;

    double riskDistance = MathAbs(entryPrice - currentTrades[tradeIndex].initialSL);
    double profitRatio = profit / riskDistance;

    // 检查是否达到追踪止损触发条件
    if(profitRatio >= InpTrailingTrigger && !currentTrades[tradeIndex].trailingActivated)
    {
        currentTrades[tradeIndex].trailingActivated = true;

        if(InpPartialClose)
        {
            // 部分平仓50%
            double currentVolume = PositionGetDouble(POSITION_VOLUME);
            double lotStep = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_STEP);
            double closeVolume = NormalizeDouble(currentVolume * 0.5 / lotStep, 0) * lotStep;

            MqlTradeRequest request = {};
            MqlTradeResult result = {};

            request.action = TRADE_ACTION_DEAL;
            request.symbol = _Symbol;
            request.volume = closeVolume;
            request.type = (posType == POSITION_TYPE_BUY) ? ORDER_TYPE_SELL : ORDER_TYPE_BUY;
            request.position = ticket;
            request.price = currentPrice;
            request.magic = InpMagicNumber;
            request.comment = "GoldPro Partial Close";

            bool partialSuccess = OrderSend(request, result);
            if(!partialSuccess)
            {
                Print("部分平仓失败: ", result.retcode, " - ", result.comment);
            }
            else
            {
                Print("部分平仓成功: ", ticket, " 数量: ", closeVolume);
            }
        }

        // 移动止损到盈亏平衡点
        ModifyStopLoss(ticket, entryPrice);
        currentTrades[tradeIndex].currentSL = entryPrice;
    }

    // 继续追踪止损
    if(currentTrades[tradeIndex].trailingActivated)
    {
        double newSL = currentTrades[tradeIndex].currentSL;
        double atr = atrValues[0];
        double trailingDistance = atr * InpTrailingStep;

        if(posType == POSITION_TYPE_BUY)
        {
            newSL = currentPrice - trailingDistance;
            if(newSL > currentTrades[tradeIndex].currentSL)
            {
                ModifyStopLoss(ticket, newSL);
                currentTrades[tradeIndex].currentSL = newSL;
            }
        }
        else
        {
            newSL = currentPrice + trailingDistance;
            if(newSL < currentTrades[tradeIndex].currentSL)
            {
                ModifyStopLoss(ticket, newSL);
                currentTrades[tradeIndex].currentSL = newSL;
            }
        }
    }
}

//+------------------------------------------------------------------+
//| 修改止损                                                         |
//+------------------------------------------------------------------+
void ModifyStopLoss(ulong ticket, double newSL)
{
    if(!PositionSelectByTicket(ticket))
        return;

    double currentSL = PositionGetDouble(POSITION_SL);
    double currentTP = PositionGetDouble(POSITION_TP);

    if(MathAbs(newSL - currentSL) < _Point)
        return;

    MqlTradeRequest request = {};
    MqlTradeResult result = {};

    request.action = TRADE_ACTION_SLTP;
    request.symbol = _Symbol;
    request.position = ticket;
    request.sl = NormalizeDouble(newSL, _Digits);
    request.tp = currentTP;
    request.magic = InpMagicNumber;

    bool success = OrderSend(request, result);
    if(!success)
    {
        Print("修改止损失败: ", result.retcode, " - ", result.comment);
    }
}

//+------------------------------------------------------------------+
//| 关闭持仓                                                         |
//+------------------------------------------------------------------+
void ClosePosition(ulong ticket)
{
    if(!PositionSelectByTicket(ticket))
        return;

    MqlTradeRequest request = {};
    MqlTradeResult result = {};

    request.action = TRADE_ACTION_DEAL;
    request.symbol = _Symbol;
    request.volume = PositionGetDouble(POSITION_VOLUME);
    request.type = (PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY) ? ORDER_TYPE_SELL : ORDER_TYPE_BUY;
    request.position = ticket;
    request.price = (PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY) ?
                    SymbolInfoDouble(_Symbol, SYMBOL_BID) : SymbolInfoDouble(_Symbol, SYMBOL_ASK);
    request.magic = InpMagicNumber;
    request.comment = "GoldPro Trend Reversal";

    bool success = OrderSend(request, result);
    if(!success)
    {
        Print("关闭持仓失败: ", result.retcode, " - ", result.comment);
    }
    else
    {
        Print("趋势反转平仓成功: ", ticket);
    }
}

//+------------------------------------------------------------------+
//| 从跟踪数组中移除交易                                             |
//+------------------------------------------------------------------+
void RemoveTradeFromTracking(int index)
{
    int size = ArraySize(currentTrades);
    if(index < 0 || index >= size)
        return;

    for(int i = index; i < size - 1; i++)
    {
        currentTrades[i] = currentTrades[i + 1];
    }

    ArrayResize(currentTrades, size - 1);
}

//+------------------------------------------------------------------+
//| 更新可视化                                                       |
//+------------------------------------------------------------------+
void UpdateVisualization()
{
    // 绘制55MA通道
    DrawMAChannel();

    // 绘制HTF MA
    if(InpUseHTFFilter)
        DrawHTF_MA();

    // 绘制交易信号
    DrawTradeSignals();

    // 显示信息面板
    ShowInfoPanel();
}

//+------------------------------------------------------------------+
//| 绘制MA通道                                                       |
//+------------------------------------------------------------------+
void DrawMAChannel()
{
    string objNameHigh = "GoldPro_MA_High";
    string objNameLow = "GoldPro_MA_Low";

    // 删除旧对象
    ObjectDelete(0, objNameHigh);
    ObjectDelete(0, objNameLow);

    // 创建MA高点线
    ObjectCreate(0, objNameHigh, OBJ_TREND, 0,
                 iTime(_Symbol, InpTimeframe, 2), ma55High[2],
                 iTime(_Symbol, InpTimeframe, 0), ma55High[0]);
    ObjectSetInteger(0, objNameHigh, OBJPROP_COLOR, clrBlue);
    ObjectSetInteger(0, objNameHigh, OBJPROP_WIDTH, 2);
    ObjectSetInteger(0, objNameHigh, OBJPROP_RAY_RIGHT, true);

    // 创建MA低点线
    ObjectCreate(0, objNameLow, OBJ_TREND, 0,
                 iTime(_Symbol, InpTimeframe, 2), ma55Low[2],
                 iTime(_Symbol, InpTimeframe, 0), ma55Low[0]);
    ObjectSetInteger(0, objNameLow, OBJPROP_COLOR, clrRed);
    ObjectSetInteger(0, objNameLow, OBJPROP_WIDTH, 2);
    ObjectSetInteger(0, objNameLow, OBJPROP_RAY_RIGHT, true);
}

//+------------------------------------------------------------------+
//| 绘制HTF MA                                                       |
//+------------------------------------------------------------------+
void DrawHTF_MA()
{
    string objName = "GoldPro_HTF_MA";
    ObjectDelete(0, objName);

    ObjectCreate(0, objName, OBJ_HLINE, 0, 0, htfMA[0]);
    ObjectSetInteger(0, objName, OBJPROP_COLOR, clrYellow);
    ObjectSetInteger(0, objName, OBJPROP_WIDTH, 1);
    ObjectSetInteger(0, objName, OBJPROP_STYLE, STYLE_DASH);
}

//+------------------------------------------------------------------+
//| 绘制交易信号                                                     |
//+------------------------------------------------------------------+
void DrawTradeSignals()
{
    // 检查买入信号
    if(CheckBuySignal())
    {
        string objName = "GoldPro_Buy_" + TimeToString(TimeCurrent());
        ObjectCreate(0, objName, OBJ_ARROW_UP, 0,
                     iTime(_Symbol, InpTimeframe, 0),
                     iLow(_Symbol, InpTimeframe, 0) - atrValues[0] * 0.5);
        ObjectSetInteger(0, objName, OBJPROP_COLOR, clrLime);
        ObjectSetInteger(0, objName, OBJPROP_WIDTH, 3);
    }

    // 检查卖出信号
    if(CheckSellSignal())
    {
        string objName = "GoldPro_Sell_" + TimeToString(TimeCurrent());
        ObjectCreate(0, objName, OBJ_ARROW_DOWN, 0,
                     iTime(_Symbol, InpTimeframe, 0),
                     iHigh(_Symbol, InpTimeframe, 0) + atrValues[0] * 0.5);
        ObjectSetInteger(0, objName, OBJPROP_COLOR, clrRed);
        ObjectSetInteger(0, objName, OBJPROP_WIDTH, 3);
    }
}

//+------------------------------------------------------------------+
//| 显示信息面板                                                     |
//+------------------------------------------------------------------+
void ShowInfoPanel()
{
    string info = "=== 黄金专业版策略 ===\n";
    info += "55MA High: " + DoubleToString(ma55High[0], _Digits) + "\n";
    info += "55MA Low: " + DoubleToString(ma55Low[0], _Digits) + "\n";
    info += "ATR: " + DoubleToString(atrValues[0], _Digits) + "\n";

    if(InpUseHTFFilter)
        info += "HTF MA: " + DoubleToString(htfMA[0], _Digits) + "\n";

    info += "Heiken Ashi: " + (heikenClose[0] > heikenOpen[0] ? "绿色(看涨)" : "红色(看跌)") + "\n";
    info += "持仓数量: " + IntegerToString(PositionsTotal()) + "\n";
    info += "追踪交易: " + IntegerToString(ArraySize(currentTrades)) + "\n";

    string objName = "GoldPro_Info";
    ObjectDelete(0, objName);

    ObjectCreate(0, objName, OBJ_LABEL, 0, 0, 0);
    ObjectSetInteger(0, objName, OBJPROP_CORNER, CORNER_LEFT_UPPER);
    ObjectSetInteger(0, objName, OBJPROP_XDISTANCE, 10);
    ObjectSetInteger(0, objName, OBJPROP_YDISTANCE, 30);
    ObjectSetString(0, objName, OBJPROP_TEXT, info);
    ObjectSetInteger(0, objName, OBJPROP_COLOR, clrWhite);
    ObjectSetInteger(0, objName, OBJPROP_FONTSIZE, 9);
}
